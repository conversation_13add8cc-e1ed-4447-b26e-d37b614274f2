"""PDF parser for extracting text from PDF documents."""

from typing import Dict, Any
import io
from .base import BaseParser, ParsingError


class PDFParser(BaseParser):
    """Parser for PDF documents."""
    
    @property
    def supported_types(self) -> list:
        """Return list of supported content types."""
        return ['pdf']
    
    def parse(self, data: bytes, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Parse PDF content and extract text.
        
        Args:
            data: Raw PDF data
            metadata: Content metadata
            
        Returns:
            Dictionary with parsed results
            
        Raises:
            ParsingError: If parsing fails
        """
        try:
            # Try pdfminer.six first (more robust)
            try:
                text = self._parse_with_pdfminer(data)
                parser_used = 'pdfminer.six'
            except Exception as e1:
                # Fallback to PyPDF2
                try:
                    text = self._parse_with_pypdf2(data)
                    parser_used = 'PyPDF2'
                except Exception as e2:
                    raise ParsingError(f"Both PDF parsers failed. pdfminer: {e1}, PyPDF2: {e2}")
            
            # Normalize the extracted text
            normalized_text = self.normalize_text(text)
            
            parsing_metadata = {
                'parser_used': parser_used,
                'original_length': len(data),
                'extracted_length': len(text),
                'normalized_length': len(normalized_text)
            }
            
            return self.create_result(normalized_text, metadata, parsing_metadata)
            
        except Exception as e:
            raise ParsingError(f"Failed to parse PDF content: {e}")
    
    def _parse_with_pdfminer(self, data: bytes) -> str:
        """Parse PDF using pdfminer.six.
        
        Args:
            data: Raw PDF data
            
        Returns:
            Extracted text
            
        Raises:
            Exception: If parsing fails
        """
        try:
            from pdfminer.high_level import extract_text
            from pdfminer.layout import LAParams
            
            # Create BytesIO object from data
            pdf_file = io.BytesIO(data)
            
            # Configure layout analysis parameters
            laparams = LAParams(
                boxes_flow=0.5,
                word_margin=0.1,
                char_margin=2.0,
                line_margin=0.5
            )
            
            # Extract text
            text = extract_text(pdf_file, laparams=laparams)
            
            return text
            
        except ImportError:
            raise Exception("pdfminer.six not available")
        except Exception as e:
            raise Exception(f"pdfminer.six parsing failed: {e}")
    
    def _parse_with_pypdf2(self, data: bytes) -> str:
        """Parse PDF using PyPDF2.
        
        Args:
            data: Raw PDF data
            
        Returns:
            Extracted text
            
        Raises:
            Exception: If parsing fails
        """
        try:
            import PyPDF2
            
            # Create BytesIO object from data
            pdf_file = io.BytesIO(data)
            
            # Create PDF reader
            pdf_reader = PyPDF2.PdfReader(pdf_file)
            
            # Extract text from all pages
            text_parts = []
            for page_num in range(len(pdf_reader.pages)):
                page = pdf_reader.pages[page_num]
                page_text = page.extract_text()
                if page_text:
                    text_parts.append(page_text)
            
            text = '\n'.join(text_parts)
            
            return text
            
        except ImportError:
            raise Exception("PyPDF2 not available")
        except Exception as e:
            raise Exception(f"PyPDF2 parsing failed: {e}")
    
    def _validate_pdf_data(self, data: bytes) -> bool:
        """Validate that data appears to be a valid PDF.
        
        Args:
            data: Raw data to validate
            
        Returns:
            True if data appears to be PDF
        """
        if len(data) < 4:
            return False
        
        # Check for PDF magic number
        return data.startswith(b'%PDF')
