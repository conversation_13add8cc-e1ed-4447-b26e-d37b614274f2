"""Factory for creating embedding providers."""

from typing import Dict, Any, Optional
from loguru import logger

from .base import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ck<PERSON>mbeddingProvider, EmbeddingError
from .providers import OpenAIEmbeddingProvider, HuggingFaceEmbeddingProvider, SentenceTransformersProvider


class EmbeddingFactory:
    """Factory for creating embedding providers."""
    
    def __init__(self):
        """Initialize embedding factory."""
        self._providers = {
            'openai': OpenAIEmbeddingProvider,
            'huggingface': HuggingFaceEmbeddingProvider,
            'sentence_transformers': SentenceTransformersProvider,
            'mock': MockEmbeddingProvider
        }
    
    def create_provider(self, provider_name: str, config: Dict[str, Any]) -> BaseEmbeddingProvider:
        """Create an embedding provider.
        
        Args:
            provider_name: Name of the provider to create
            config: Provider configuration
            
        Returns:
            Embedding provider instance
            
        Raises:
            EmbeddingError: If provider is not supported
        """
        provider_class = self._providers.get(provider_name.lower())
        
        if not provider_class:
            available_providers = list(self._providers.keys())
            raise EmbeddingError(
                f"Unsupported embedding provider: {provider_name}. "
                f"Available providers: {available_providers}"
            )
        
        try:
            provider = provider_class(config)
            logger.info(f"Created embedding provider: {provider_name}")
            return provider
        except Exception as e:
            raise EmbeddingError(f"Failed to create embedding provider {provider_name}: {e}")
    
    def get_available_providers(self) -> list:
        """Get list of available embedding providers.
        
        Returns:
            List of provider names
        """
        return list(self._providers.keys())
    
    def validate_provider_config(self, provider_name: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """Validate configuration for a specific provider.
        
        Args:
            provider_name: Name of the provider
            config: Provider configuration
            
        Returns:
            Validation results
        """
        try:
            provider = self.create_provider(provider_name, config)
            return provider.validate_configuration()
        except Exception as e:
            return {
                'valid': False,
                'provider': provider_name,
                'errors': [str(e)],
                'warnings': []
            }
