"""Base parser class and common utilities."""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
import re
from loguru import logger


class ParsingError(Exception):
    """Custom exception for parsing errors."""
    pass


class BaseParser(ABC):
    """Abstract base class for content parsers."""
    
    def __init__(self, max_text_length: int = 1000000):
        """Initialize parser.
        
        Args:
            max_text_length: Maximum allowed extracted text length
        """
        self.max_text_length = max_text_length
    
    @abstractmethod
    def parse(self, data: bytes, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Parse content and extract text.
        
        Args:
            data: Raw content data
            metadata: Content metadata
            
        Returns:
            Dictionary with parsed results including 'normalized_text'
            
        Raises:
            ParsingError: If parsing fails
        """
        pass
    
    @property
    @abstractmethod
    def supported_types(self) -> list:
        """Return list of supported content types."""
        pass
    
    def normalize_text(self, text: str) -> str:
        """Normalize extracted text.
        
        Args:
            text: Raw extracted text
            
        Returns:
            Normalized text
        """
        if not text:
            return ""
        
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove control characters except newlines and tabs
        text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)
        
        # Normalize line endings
        text = text.replace('\r\n', '\n').replace('\r', '\n')
        
        # Remove excessive newlines
        text = re.sub(r'\n{3,}', '\n\n', text)
        
        # Strip leading/trailing whitespace
        text = text.strip()
        
        # Truncate if too long
        if len(text) > self.max_text_length:
            logger.warning(f"Text truncated from {len(text)} to {self.max_text_length} characters")
            text = text[:self.max_text_length] + "... [TRUNCATED]"
        
        return text
    
    def create_result(self, normalized_text: str, metadata: Dict[str, Any], 
                     parsing_metadata: Dict[str, Any] = None) -> Dict[str, Any]:
        """Create standardized parsing result.
        
        Args:
            normalized_text: Extracted and normalized text
            metadata: Original content metadata
            parsing_metadata: Additional parsing metadata
            
        Returns:
            Standardized result dictionary
        """
        result = {
            'normalized_text': normalized_text,
            'text_length': len(normalized_text),
            'parsing_successful': True,
            'parser_type': self.__class__.__name__,
            'original_metadata': metadata
        }
        
        if parsing_metadata:
            result['parsing_metadata'] = parsing_metadata
        
        return result
    
    def handle_parsing_error(self, error: Exception, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Handle parsing errors and create error result.
        
        Args:
            error: The parsing error
            metadata: Original content metadata
            
        Returns:
            Error result dictionary
        """
        logger.error(f"Parsing failed with {self.__class__.__name__}: {error}")
        
        return {
            'normalized_text': "",
            'text_length': 0,
            'parsing_successful': False,
            'parser_type': self.__class__.__name__,
            'error': str(error),
            'original_metadata': metadata
        }
    
    def detect_encoding(self, data: bytes) -> str:
        """Detect text encoding of data.
        
        Args:
            data: Raw data bytes
            
        Returns:
            Detected encoding name
        """
        # Try common encodings in order of preference
        encodings = ['utf-8', 'utf-16', 'utf-16le', 'utf-16be', 'latin1', 'cp1252']
        
        for encoding in encodings:
            try:
                data.decode(encoding)
                return encoding
            except UnicodeDecodeError:
                continue
        
        # Fallback to utf-8 with error handling
        return 'utf-8'
    
    def safe_decode(self, data: bytes, encoding: str = None) -> str:
        """Safely decode bytes to string.
        
        Args:
            data: Raw data bytes
            encoding: Encoding to use (auto-detect if None)
            
        Returns:
            Decoded string
        """
        if encoding is None:
            encoding = self.detect_encoding(data)
        
        try:
            return data.decode(encoding)
        except UnicodeDecodeError:
            # Fallback with error replacement
            return data.decode(encoding, errors='replace')
