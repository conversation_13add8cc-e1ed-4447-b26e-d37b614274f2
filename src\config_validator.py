"""Configuration validation for Niche Text ETL with graceful degradation."""

import smtplib
from typing import Dict, List
from loguru import logger

from .config import Config


class ValidationResult:
    """Container for validation results."""
    
    def __init__(self):
        self.valid = True
        self.warnings: List[str] = []
        self.errors: List[str] = []
        self.module_flags: Dict[str, bool] = {}
    
    def add_warning(self, message: str):
        """Add a warning message."""
        self.warnings.append(message)
        logger.warning(f"Configuration warning: {message}")
    
    def add_error(self, message: str):
        """Add an error message."""
        self.errors.append(message)
        self.valid = False
        logger.error(f"Configuration error: {message}")
    
    def set_module_flag(self, module: str, enabled: bool):
        """Set module enabled/disabled flag."""
        self.module_flags[module] = enabled
        status = "enabled" if enabled else "disabled"
        logger.info(f"Module '{module}' is {status}")


class ConfigurationValidator:
    """Validates ETL pipeline configuration with graceful degradation."""
    
    def __init__(self, config: Config):
        """Initialize validator with configuration.
        
        Args:
            config: Configuration object to validate
        """
        self.config = config
        self.result = ValidationResult()
    
    def validate_all(self) -> ValidationResult:
        """Validate all configuration modules.
        
        Returns:
            ValidationResult with overall validation status and module flags
        """
        logger.info("Starting comprehensive configuration validation...")
        
        # Validate core configuration
        self._validate_core_config()
        
        # Validate email module
        self._validate_email_module()
        
        # Validate text embedding module
        self._validate_embedding_module()
        
        # Validate database configuration
        self._validate_database_config()
        
        # Log summary
        self._log_validation_summary()
        
        return self.result
    
    def _validate_core_config(self):
        """Validate core configuration sections."""
        logger.debug("Validating core configuration...")
        
        # Check required sections exist
        required_sections = ['database', 'processing', 'logging', 'pipeline']
        for section in required_sections:
            if not hasattr(self.config, section):
                self.result.add_error(f"Missing required configuration section: {section}")
        
        # Validate processing configuration
        if hasattr(self.config, 'processing'):
            if self.config.processing.batch_size <= 0:
                self.result.add_error("Processing batch_size must be greater than 0")
            
            if self.config.processing.max_workers <= 0:
                self.result.add_error("Processing max_workers must be greater than 0")
            
            if not self.config.processing.categories_to_process:
                self.result.add_warning("No categories_to_process specified - pipeline may not process any documents")
    
    def _validate_email_module(self):
        """Validate email module configuration with graceful degradation."""
        logger.debug("Validating email module configuration...")
        
        email_enabled = True
        missing_params = []
        
        # Check required email parameters
        required_email_params = {
            'smtp_server': getattr(self.config.email, 'smtp_server', None),
            'smtp_port': getattr(self.config.email, 'smtp_port', None),
            'sender': getattr(self.config.email, 'sender', None),
            'recipients': getattr(self.config.email, 'recipients', None)
        }

        # Note: Email authentication (username/password) is optional and validated during SMTP test
        
        for param, value in required_email_params.items():
            if not value:
                missing_params.append(param)
        
        # Check recipients is a non-empty list
        if required_email_params['recipients']:
            if not isinstance(required_email_params['recipients'], list) or len(required_email_params['recipients']) == 0:
                missing_params.append('recipients (must be non-empty list)')
        
        if missing_params:
            email_enabled = False
            self.result.add_warning(
                f"Email module disabled - missing required parameters: {', '.join(missing_params)}"
            )
        else:
            # Test SMTP connectivity if all parameters are present
            try:
                self._test_smtp_connection()
                logger.info("SMTP connection test successful")
            except Exception as e:
                email_enabled = False
                self.result.add_warning(f"Email module disabled - SMTP connection failed: {e}")
        
        self.result.set_module_flag('email_enabled', email_enabled)
    
    def _validate_embedding_module(self):
        """Validate text embedding module configuration with graceful degradation."""
        logger.debug("Validating text embedding module configuration...")
        
        embedding_enabled = True
        missing_params = []
        
        # Check if embedding is explicitly disabled
        if not getattr(self.config.embedding, 'enabled', True):
            embedding_enabled = False
            self.result.add_warning("Text embedding module disabled by configuration")
        else:
            # Check required embedding parameters
            required_embedding_params = {
                'model_name': getattr(self.config.embedding.model, 'model_name', None),
                'embedding_dimension': getattr(self.config.embedding.storage, 'embedding_dimension', None),
                'batch_size': getattr(self.config.embedding.processing, 'batch_size', None)
            }
            
            for param, value in required_embedding_params.items():
                if not value:
                    missing_params.append(param)
            
            if missing_params:
                embedding_enabled = False
                self.result.add_warning(
                    f"Text embedding module disabled - missing required parameters: {', '.join(missing_params)}"
                )
            else:
                # Test model accessibility
                try:
                    self._test_embedding_model()
                    logger.info("Text embedding model accessibility test successful")
                except Exception as e:
                    embedding_enabled = False
                    self.result.add_warning(f"Text embedding module disabled - model test failed: {e}")
        
        self.result.set_module_flag('embedding_enabled', embedding_enabled)
    
    def _validate_database_config(self):
        """Validate database configuration."""
        logger.debug("Validating database configuration...")
        
        # Check database connections exist
        if not hasattr(self.config, 'database') or not self.config.database:
            self.result.add_error("Database configuration is missing")
            return
        
        required_db_types = ['source', 'destination']
        for db_type in required_db_types:
            if db_type not in self.config.database:
                self.result.add_error(f"Missing {db_type} database configuration")
            else:
                db_config = self.config.database[db_type]
                if not db_config.server or not db_config.database:
                    self.result.add_error(f"{db_type} database missing server or database name")

                # Validate schema name
                if not db_config.db_schema or not db_config.db_schema.strip():
                    self.result.add_error(f"{db_type} database schema cannot be empty")
                elif not db_config.db_schema.replace('_', '').replace('-', '').isalnum():
                    self.result.add_warning(f"{db_type} database schema '{db_config.db_schema}' contains special characters")
    
    def _test_smtp_connection(self):
        """Test SMTP connection without sending email."""
        try:
            with smtplib.SMTP(self.config.email.smtp_server, self.config.email.smtp_port) as server:
                if getattr(self.config.email, 'use_tls', True):
                    server.starttls()
                # Just test connection, don't authenticate
                server.noop()
        except Exception as e:
            raise Exception(f"SMTP connection test failed: {e}")
    
    def _test_embedding_model(self):
        """Test text embedding model accessibility."""
        try:
            # Import here to avoid dependency issues if not installed
            from sentence_transformers import SentenceTransformer
            
            model_name = self.config.embedding.model.model_name
            
            # Try to load the model (this will download if not cached)
            model = SentenceTransformer(model_name)
            
            # Test with a simple sentence
            test_embedding = model.encode("test sentence")
            
            # Verify embedding dimension matches configuration
            expected_dim = self.config.embedding.storage.embedding_dimension
            if len(test_embedding) != expected_dim:
                raise Exception(
                    f"Model embedding dimension {len(test_embedding)} does not match "
                    f"configured dimension {expected_dim}"
                )
                
        except ImportError:
            raise Exception("sentence-transformers library not available")
        except Exception as e:
            raise Exception(f"Model test failed: {e}")
    
    def _log_validation_summary(self):
        """Log validation summary."""
        logger.info("Configuration validation completed")
        logger.info(f"Overall validation status: {'PASSED' if self.result.valid else 'FAILED'}")
        logger.info(f"Warnings: {len(self.result.warnings)}")
        logger.info(f"Errors: {len(self.result.errors)}")
        
        # Log module status
        logger.info("Module status:")
        for module, enabled in self.result.module_flags.items():
            status = "ENABLED" if enabled else "DISABLED"
            logger.info(f"  - {module}: {status}")
        
        if self.result.warnings:
            logger.warning("Configuration warnings found:")
            for warning in self.result.warnings:
                logger.warning(f"  - {warning}")
        
        if self.result.errors:
            logger.error("Configuration errors found:")
            for error in self.result.errors:
                logger.error(f"  - {error}")
