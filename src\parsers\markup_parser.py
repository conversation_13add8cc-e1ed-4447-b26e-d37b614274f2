"""Markup parser for HTML/XML content."""

from typing import Dict, Any
import io
from bs4 import BeautifulSoup
import html2text
from .base import BaseParser, ParsingError


class MarkupParser(BaseParser):
    """Parser for HTML/XML markup content."""
    
    def __init__(self, max_text_length: int = 1000000):
        """Initialize markup parser."""
        super().__init__(max_text_length)
        
        # Configure html2text converter
        self.html_converter = html2text.HTML2Text()
        self.html_converter.ignore_links = True
        self.html_converter.ignore_images = True
        self.html_converter.ignore_emphasis = True
        self.html_converter.body_width = 0  # No line wrapping
    
    @property
    def supported_types(self) -> list:
        """Return list of supported content types."""
        return ['markup', 'html', 'htm', 'xml', 'json', 'yaml']
    
    def parse(self, data: bytes, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Parse markup content and extract text.
        
        Args:
            data: Raw markup data
            metadata: Content metadata
            
        Returns:
            Dictionary with parsed results
            
        Raises:
            ParsingError: If parsing fails
        """
        try:
            # Decode the content
            content = self.safe_decode(data)
            
            # Determine content type and parse accordingly
            content_type = self._detect_markup_type(content)
            
            if content_type == 'html':
                text = self._parse_html(content)
            elif content_type == 'xml':
                text = self._parse_xml(content)
            elif content_type == 'json':
                text = self._parse_json(content)
            elif content_type == 'yaml':
                text = self._parse_yaml(content)
            else:
                # Fallback to generic text extraction
                text = self._extract_text_generic(content)
            
            # Normalize the extracted text
            normalized_text = self.normalize_text(text)
            
            parsing_metadata = {
                'detected_type': content_type,
                'encoding_used': self.detect_encoding(data),
                'original_length': len(data),
                'content_length': len(content),
                'extracted_length': len(text),
                'normalized_length': len(normalized_text)
            }
            
            return self.create_result(normalized_text, metadata, parsing_metadata)
            
        except Exception as e:
            raise ParsingError(f"Failed to parse markup content: {e}")
    
    def _detect_markup_type(self, content: str) -> str:
        """Detect the type of markup content.
        
        Args:
            content: Decoded content string
            
        Returns:
            Detected content type
        """
        content_lower = content.lower().strip()
        
        if content_lower.startswith('<!doctype html') or '<html' in content_lower[:200]:
            return 'html'
        elif content_lower.startswith('<?xml') or content_lower.startswith('<'):
            return 'xml'
        elif content.strip().startswith('{') or content.strip().startswith('['):
            return 'json'
        elif ':' in content and ('\n' in content or '\r' in content):
            return 'yaml'
        else:
            return 'unknown'
    
    def _parse_html(self, content: str) -> str:
        """Parse HTML content and extract text.
        
        Args:
            content: HTML content string
            
        Returns:
            Extracted text
        """
        try:
            # Use html2text for better formatting preservation
            text = self.html_converter.handle(content)
            return text
        except Exception:
            # Fallback to BeautifulSoup
            return self._extract_text_with_soup(content, 'html.parser')
    
    def _parse_xml(self, content: str) -> str:
        """Parse XML content and extract text.
        
        Args:
            content: XML content string
            
        Returns:
            Extracted text
        """
        return self._extract_text_with_soup(content, 'xml')
    
    def _extract_text_with_soup(self, content: str, parser: str) -> str:
        """Extract text using BeautifulSoup.
        
        Args:
            content: Content string
            parser: Parser type for BeautifulSoup
            
        Returns:
            Extracted text
        """
        try:
            soup = BeautifulSoup(content, parser)
            
            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()
            
            # Get text and clean it up
            text = soup.get_text()
            
            # Clean up whitespace
            lines = (line.strip() for line in text.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text = ' '.join(chunk for chunk in chunks if chunk)
            
            return text
            
        except Exception as e:
            raise ParsingError(f"BeautifulSoup parsing failed: {e}")
    
    def _parse_json(self, content: str) -> str:
        """Parse JSON content and extract readable text.
        
        Args:
            content: JSON content string
            
        Returns:
            Extracted text representation
        """
        import json
        
        try:
            data = json.loads(content)
            return self._extract_text_from_json(data)
        except json.JSONDecodeError:
            # If not valid JSON, treat as plain text
            return content
    
    def _extract_text_from_json(self, data, prefix="") -> str:
        """Recursively extract text from JSON data structure.
        
        Args:
            data: JSON data structure
            prefix: Current key prefix for nested structures
            
        Returns:
            Text representation of JSON data
        """
        text_parts = []
        
        if isinstance(data, dict):
            for key, value in data.items():
                current_prefix = f"{prefix}.{key}" if prefix else key
                if isinstance(value, (dict, list)):
                    text_parts.append(f"{current_prefix}:")
                    text_parts.append(self._extract_text_from_json(value, current_prefix))
                else:
                    text_parts.append(f"{current_prefix}: {value}")
        elif isinstance(data, list):
            for i, item in enumerate(data):
                current_prefix = f"{prefix}[{i}]" if prefix else f"[{i}]"
                text_parts.append(self._extract_text_from_json(item, current_prefix))
        else:
            text_parts.append(str(data))
        
        return "\n".join(text_parts)
    
    def _parse_yaml(self, content: str) -> str:
        """Parse YAML content and extract readable text.
        
        Args:
            content: YAML content string
            
        Returns:
            Extracted text representation
        """
        try:
            import yaml
            data = yaml.safe_load(content)
            return self._extract_text_from_json(data)  # Reuse JSON extraction logic
        except Exception:
            # If YAML parsing fails, treat as plain text
            return content
    
    def _extract_text_generic(self, content: str) -> str:
        """Generic text extraction for unknown markup types.
        
        Args:
            content: Content string
            
        Returns:
            Extracted text
        """
        # Try BeautifulSoup with html parser as fallback
        try:
            return self._extract_text_with_soup(content, 'html.parser')
        except Exception:
            # Last resort: return content as-is
            return content
