# Niche Text ETL Pipeline

A comprehensive ETL (Extract, Transform, Load) pipeline for processing and normalizing text documents from Niche RMS database with advanced features including text embedding generation, incremental processing, and comprehensive monitoring.

## Features

- **Comprehensive Configuration Validation**: Validates email and text embedding modules with graceful degradation
- **4-Part Database Naming Convention**: Uses `[server].[database].[schema].[table]` for all database operations
- **Text Embedding Support**: Generates embeddings using sentence-transformers (all-MiniLM-L6-v2 model)
- **Incremental Processing**: Resumes from last processed record using checkpoints
- **Multi-threaded Processing**: Concurrent document processing with ThreadPoolExecutor
- **Document Format Support**: MS Word, Excel, PDF, HTML, XML, and Niche-specific formats
- **Comprehensive Monitoring**: Email alerts, log analysis, and performance tracking
- **Graceful Error Handling**: Continues processing despite individual document failures

## Quick Start

1. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Configure Database Connections and Email Settings**
   Edit `config.yaml` with your database connection details and email configuration:
   ```yaml
   database:
     source:
       server: "YOUR_SOURCE_SERVER"
       database: "YOUR_SOURCE_DATABASE"
       db_schema: "dbo"  # or your custom schema
       username: "YOUR_USERNAME"
       password: "YOUR_PASSWORD"
     destination:
       server: "YOUR_DEST_SERVER"
       database: "YOUR_DEST_DATABASE"
       db_schema: "dbo"  # or your custom schema
       username: "YOUR_USERNAME"
       password: "YOUR_PASSWORD"

   email:
     smtp_server: "smtp.example.com"
     smtp_port: 587
     sender: "<EMAIL>"
     username: "smtp_username"  # SMTP authentication
     password: "smtp_password"  # SMTP authentication
     recipients:
       - "<EMAIL>"
   ```

3. **Run the Pipeline**
   ```bash
   python main.py
   ```

## Configuration

### Centralized Configuration
All configuration is managed through a single `config.yaml` file, providing a centralized and straightforward approach to configuration management. No environment variables or additional configuration files are required.

### Email Module Configuration
The pipeline validates email configuration and enables/disables email functionality based on availability:

```yaml
email:
  smtp_server: "smtp.example.com"
  smtp_port: 587
  sender: "<EMAIL>"
  username: "smtp_username"  # SMTP authentication (optional)
  password: "smtp_password"  # SMTP authentication (optional)
  recipients:
    - "<EMAIL>"
```

**Required Parameters**: `smtp_server`, `smtp_port`, `sender`, `recipients` (non-empty list)
**Optional Parameters**: `username`, `password` (for SMTP authentication)

### Text Embedding Configuration
Text embedding functionality is validated and can be gracefully disabled:

```yaml
embedding:
  enabled: true
  model:
    model_name: "all-MiniLM-L6-v2"
  processing:
    batch_size: 32
  storage:
    embedding_dimension: 384
```

**Required Parameters**: `model_name`, `embedding_dimension`, `batch_size`

### Database Schema Configuration
Configure custom schemas for source and destination databases:

```yaml
database:
  source:
    db_schema: "source_schema"    # Custom source schema
  destination:
    db_schema: "destination_schema" # Custom destination schema
```

**Schema Validation**:
- Schema names cannot be empty
- Special characters are allowed but warned about
- Both source and destination schemas are independently configurable

### Database Naming Convention
All database operations use 4-part naming: `[server].[database].[schema].[table]`

The schema is configurable for both source and destination databases:

```yaml
database:
  source:
    db_schema: "dbo"        # Default schema
  destination:
    db_schema: "etl_schema" # Custom schema example
```

Examples:
- Default: `[SQLSERVER01].[MyDatabase].[dbo].[MyTable]`
- Custom: `[SQLSERVER01].[MyDatabase].[etl_schema].[MyTable]`

**Schema Configuration**:
- **Default Value**: "dbo" (maintains backward compatibility)
- **Validation**: Schema names must be non-empty strings
- **Flexibility**: Different schemas can be used for source and destination databases

## Architecture

### Core Components

- **ConfigurationValidator**: Validates all configuration modules with graceful degradation
- **NicheTextPipeline**: Main pipeline orchestrator with threading support
- **DbClient**: Database client with 4-part naming convention support
- **ParserFactory**: Document parser factory supporting multiple formats
- **EmbeddingProcessor**: Text embedding generation (optional)
- **Monitor**: Comprehensive monitoring and alerting system

### Processing Flow

1. **Configuration Validation**: Validates email and embedding modules
2. **Database Connection**: Tests source and destination connections
3. **Incremental Processing**: Resumes from last checkpoint
4. **Document Processing**: Multi-threaded extraction and normalization
5. **Embedding Generation**: Optional text embedding creation
6. **Data Storage**: Batch insertion with 4-part naming
7. **Monitoring**: Log analysis and email alerts

## Module Status

The pipeline provides graceful degradation for optional modules:

- **Email Module**: Disabled if SMTP configuration is incomplete or invalid
- **Text Embedding Module**: Disabled if model or configuration is unavailable
- **Core Pipeline**: Always enabled with comprehensive error handling

Module status is logged at startup:
```
INFO | Module 'email_enabled': ENABLED
INFO | Module 'embedding_enabled': DISABLED
```

## Database Schema

The pipeline creates the following tables with 4-part naming:

- **Main Documents Table**: Processed document records with metadata
- **Checkpoints Table**: Incremental processing state
- **Statistics Table**: Processing performance metrics
- **Embeddings Table**: Document chunk embeddings (optional)

## Monitoring and Alerting

- **Log Analysis**: Automatic error categorization and counting
- **Email Alerts**: Configurable error threshold notifications
- **Performance Metrics**: Processing speed and success rate tracking
- **Log Rotation**: Automatic cleanup of old log files

## Error Handling

- **Graceful Degradation**: Continues processing with disabled modules
- **Individual Document Failures**: Logs errors but continues batch processing
- **Database Connection Issues**: Comprehensive connection testing and retry logic
- **Configuration Validation**: Clear error messages for missing parameters

## Development

### Project Structure
```
niche_text_etl/
├── src/
│   ├── config.py              # Configuration management
│   ├── config_validator.py    # Configuration validation
│   ├── pipeline.py            # Main pipeline logic
│   ├── database.py            # Database operations
│   ├── monitoring.py          # Monitoring and alerting
│   ├── embedding/             # Text embedding modules
│   └── parsers/               # Document parsers
├── sql/
│   └── create_destination_tables.sql
├── config.yaml                # Main configuration
├── main.py                    # Entry point
└── requirements.txt           # Dependencies
```

### Adding New Document Parsers
1. Create parser class inheriting from `BaseParser`
2. Register in `ParserFactory`
3. Add category mapping in configuration

### Extending Monitoring
1. Add new analysis methods to `LogAnalyzer`
2. Extend email templates in `EmailAlerter`
3. Configure alert thresholds in `config.yaml`

## Troubleshooting

### Common Issues

1. **Email Module Disabled**
   - Check SMTP server connectivity
   - Verify email configuration parameters
   - Test SMTP authentication

2. **Text Embedding Module Disabled**
   - Ensure sentence-transformers is installed
   - Verify model accessibility
   - Check embedding dimension configuration

3. **Database Connection Failures**
   - Verify server names and credentials
   - Check network connectivity
   - Ensure proper SQL Server drivers

### Log Analysis
Check `logs/niche_etl.log` for detailed error information and processing statistics.

## License

This project is proprietary software developed for Niche RMS text processing.
