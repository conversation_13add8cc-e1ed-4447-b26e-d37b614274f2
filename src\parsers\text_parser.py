"""Text parser for plain text content."""

from typing import Dict, Any
from .base import BaseParser, ParsingError


class TextParser(BaseParser):
    """Parser for plain text content."""
    
    @property
    def supported_types(self) -> list:
        """Return list of supported content types."""
        return ['text', 'txt']
    
    def parse(self, data: bytes, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Parse plain text content.
        
        Args:
            data: Raw text data
            metadata: Content metadata
            
        Returns:
            Dictionary with parsed results
            
        Raises:
            ParsingError: If parsing fails
        """
        try:
            # Detect and decode text
            text = self.safe_decode(data)
            
            # Normalize the text
            normalized_text = self.normalize_text(text)
            
            parsing_metadata = {
                'encoding_used': self.detect_encoding(data),
                'original_length': len(data),
                'decoded_length': len(text),
                'normalized_length': len(normalized_text)
            }
            
            return self.create_result(normalized_text, metadata, parsing_metadata)
            
        except Exception as e:
            raise ParsingError(f"Failed to parse text content: {e}")
