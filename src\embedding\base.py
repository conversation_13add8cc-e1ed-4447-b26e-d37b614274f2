"""Base classes for text embedding providers."""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Tuple
import numpy as np
from loguru import logger


class EmbeddingError(Exception):
    """Custom exception for embedding-related errors."""
    pass


class BaseEmbeddingProvider(ABC):
    """Abstract base class for text embedding providers."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize embedding provider.
        
        Args:
            config: Provider-specific configuration
        """
        self.config = config
        self.model_name = config.get('model_name', 'default')
        self.max_tokens = config.get('max_tokens', 8192)
        self.timeout = config.get('timeout', 30)
        self.max_retries = config.get('max_retries', 3)
        self.rate_limit_delay = config.get('rate_limit_delay', 1)
    
    @abstractmethod
    def embed_text(self, text: str) -> np.ndarray:
        """Generate embedding for a single text.
        
        Args:
            text: Input text to embed
            
        Returns:
            Embedding vector as numpy array
            
        Raises:
            EmbeddingError: If embedding generation fails
        """
        pass
    
    @abstractmethod
    def embed_batch(self, texts: List[str]) -> List[np.ndarray]:
        """Generate embeddings for a batch of texts.
        
        Args:
            texts: List of input texts to embed
            
        Returns:
            List of embedding vectors as numpy arrays
            
        Raises:
            EmbeddingError: If embedding generation fails
        """
        pass
    
    @abstractmethod
    def get_embedding_dimension(self) -> int:
        """Get the dimension of embeddings produced by this provider.
        
        Returns:
            Embedding dimension
        """
        pass
    
    @abstractmethod
    def validate_configuration(self) -> Dict[str, Any]:
        """Validate provider configuration.
        
        Returns:
            Dictionary with validation results
        """
        pass
    
    def preprocess_text(self, text: str) -> str:
        """Preprocess text before embedding.
        
        Args:
            text: Raw input text
            
        Returns:
            Preprocessed text
        """
        if not text:
            return ""
        
        # Basic preprocessing
        text = text.strip()
        
        # Truncate if too long (rough token estimation: 1 token ≈ 4 characters)
        max_chars = self.max_tokens * 4
        if len(text) > max_chars:
            text = text[:max_chars]
            logger.warning(f"Text truncated to {max_chars} characters for embedding")
        
        return text
    
    def chunk_text(self, text: str, chunk_size: int = 1000, overlap: int = 100) -> List[str]:
        """Split text into overlapping chunks for large documents.
        
        Args:
            text: Input text to chunk
            chunk_size: Size of each chunk in characters
            overlap: Overlap between chunks in characters
            
        Returns:
            List of text chunks
        """
        if len(text) <= chunk_size:
            return [text]
        
        chunks = []
        start = 0
        
        while start < len(text):
            end = start + chunk_size
            
            # Try to break at word boundary
            if end < len(text):
                # Look for last space within the chunk
                last_space = text.rfind(' ', start, end)
                if last_space > start:
                    end = last_space
            
            chunk = text[start:end].strip()
            if chunk:
                chunks.append(chunk)
            
            # Move start position with overlap
            start = end - overlap
            if start >= len(text):
                break
        
        return chunks
    
    def create_embedding_metadata(self, text: str, embedding: np.ndarray) -> Dict[str, Any]:
        """Create metadata for an embedding.
        
        Args:
            text: Original text
            embedding: Generated embedding
            
        Returns:
            Metadata dictionary
        """
        return {
            'text_length': len(text),
            'embedding_dimension': len(embedding),
            'model_name': self.model_name,
            'provider': self.__class__.__name__,
            'text_preview': text[:200] + "..." if len(text) > 200 else text
        }


class MockEmbeddingProvider(BaseEmbeddingProvider):
    """Mock embedding provider for testing and placeholder functionality."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize mock provider."""
        super().__init__(config)
        self.embedding_dim = config.get('embedding_dimension', 1536)
    
    def embed_text(self, text: str) -> np.ndarray:
        """Generate mock embedding for text.
        
        Args:
            text: Input text
            
        Returns:
            Random embedding vector
        """
        logger.debug(f"Generating mock embedding for text of length {len(text)}")
        
        # Generate deterministic "embedding" based on text hash
        text_hash = hash(text) % (2**31)  # Ensure positive
        np.random.seed(text_hash)
        embedding = np.random.normal(0, 1, self.embedding_dim).astype(np.float32)
        
        # Normalize to unit vector (common for embeddings)
        norm = np.linalg.norm(embedding)
        if norm > 0:
            embedding = embedding / norm
        
        return embedding
    
    def embed_batch(self, texts: List[str]) -> List[np.ndarray]:
        """Generate mock embeddings for batch of texts.
        
        Args:
            texts: List of input texts
            
        Returns:
            List of mock embedding vectors
        """
        logger.debug(f"Generating mock embeddings for batch of {len(texts)} texts")
        return [self.embed_text(text) for text in texts]
    
    def get_embedding_dimension(self) -> int:
        """Get embedding dimension.
        
        Returns:
            Embedding dimension
        """
        return self.embedding_dim
    
    def validate_configuration(self) -> Dict[str, Any]:
        """Validate mock provider configuration.
        
        Returns:
            Validation results
        """
        return {
            'valid': True,
            'provider': 'MockEmbeddingProvider',
            'embedding_dimension': self.embedding_dim,
            'warnings': ['This is a mock provider for testing only']
        }
