"""Word document parser for extracting text from MS Word documents."""

from typing import Dict, Any
import io
from .base import BaseParser, ParsingError


class WordParser(BaseParser):
    """Parser for Microsoft Word documents."""
    
    @property
    def supported_types(self) -> list:
        """Return list of supported content types."""
        return ['ms_word', 'doc', 'docx']
    
    def parse(self, data: bytes, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Parse Word document and extract text.
        
        Args:
            data: Raw Word document data
            metadata: Content metadata
            
        Returns:
            Dictionary with parsed results
            
        Raises:
            ParsingError: If parsing fails
        """
        try:
            # Try python-docx first (for .docx files)
            try:
                text = self._parse_with_python_docx(data)
                parser_used = 'python-docx'
            except Exception as e1:
                # Fallback to textract (handles both .doc and .docx)
                try:
                    text = self._parse_with_textract(data)
                    parser_used = 'textract'
                except Exception as e2:
                    raise ParsingError(f"Both Word parsers failed. python-docx: {e1}, textract: {e2}")
            
            # Normalize the extracted text
            normalized_text = self.normalize_text(text)
            
            parsing_metadata = {
                'parser_used': parser_used,
                'original_length': len(data),
                'extracted_length': len(text),
                'normalized_length': len(normalized_text)
            }
            
            return self.create_result(normalized_text, metadata, parsing_metadata)
            
        except Exception as e:
            raise ParsingError(f"Failed to parse Word document: {e}")
    
    def _parse_with_python_docx(self, data: bytes) -> str:
        """Parse Word document using python-docx.
        
        Args:
            data: Raw Word document data
            
        Returns:
            Extracted text
            
        Raises:
            Exception: If parsing fails
        """
        try:
            from docx import Document
            
            # Create BytesIO object from data
            doc_file = io.BytesIO(data)
            
            # Load document
            document = Document(doc_file)
            
            # Extract text from paragraphs
            text_parts = []
            
            # Extract paragraph text
            for paragraph in document.paragraphs:
                if paragraph.text.strip():
                    text_parts.append(paragraph.text)
            
            # Extract text from tables
            for table in document.tables:
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        if cell.text.strip():
                            row_text.append(cell.text.strip())
                    if row_text:
                        text_parts.append(' | '.join(row_text))
            
            # Extract text from headers and footers
            for section in document.sections:
                # Header
                if section.header:
                    for paragraph in section.header.paragraphs:
                        if paragraph.text.strip():
                            text_parts.append(f"[HEADER] {paragraph.text}")
                
                # Footer
                if section.footer:
                    for paragraph in section.footer.paragraphs:
                        if paragraph.text.strip():
                            text_parts.append(f"[FOOTER] {paragraph.text}")
            
            text = '\n'.join(text_parts)
            
            return text
            
        except ImportError:
            raise Exception("python-docx not available")
        except Exception as e:
            raise Exception(f"python-docx parsing failed: {e}")
    
    def _parse_with_textract(self, data: bytes) -> str:
        """Parse Word document using textract.
        
        Args:
            data: Raw Word document data
            
        Returns:
            Extracted text
            
        Raises:
            Exception: If parsing fails
        """
        try:
            import textract
            import tempfile
            import os
            
            # textract requires a file path, so create a temporary file
            with tempfile.NamedTemporaryFile(delete=False, suffix='.docx') as temp_file:
                temp_file.write(data)
                temp_file_path = temp_file.name
            
            try:
                # Extract text using textract
                text_bytes = textract.process(temp_file_path)
                text = text_bytes.decode('utf-8')
                
                return text
                
            finally:
                # Clean up temporary file
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)
            
        except ImportError:
            raise Exception("textract not available")
        except Exception as e:
            raise Exception(f"textract parsing failed: {e}")
    
    def _validate_word_data(self, data: bytes) -> bool:
        """Validate that data appears to be a valid Word document.
        
        Args:
            data: Raw data to validate
            
        Returns:
            True if data appears to be a Word document
        """
        if len(data) < 4:
            return False
        
        # Check for common Word document signatures
        # .docx files start with PK (ZIP signature)
        if data.startswith(b'PK'):
            return True
        
        # .doc files have different signatures
        if data.startswith(b'\xd0\xcf\x11\xe0'):  # OLE2 signature
            return True
        
        return False
