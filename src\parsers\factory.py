"""Parser factory for creating appropriate parsers based on content type."""

from typing import Dict, Any, Optional
from loguru import logger

from .base import BaseParser, ParsingError
from .text_parser import TextParser
from .markup_parser import MarkupParser
from .niche_markup_parser import <PERSON><PERSON>MarkupParser
from .pdf_parser import PDF<PERSON>arser
from .word_parser import WordParser
from .excel_parser import ExcelParser


class ParserFactory:
    """Factory for creating content parsers based on content type."""
    
    def __init__(self, max_text_length: int = 1000000):
        """Initialize parser factory.
        
        Args:
            max_text_length: Maximum allowed extracted text length
        """
        self.max_text_length = max_text_length
        self._parsers = {}
        self._initialize_parsers()
    
    def _initialize_parsers(self):
        """Initialize all available parsers."""
        parser_classes = [
            TextParser,
            MarkupParser,
            <PERSON><PERSON>MarkupPars<PERSON>,
            PDFParser,
            WordParser,
            ExcelParser
        ]
        
        for parser_class in parser_classes:
            try:
                parser = parser_class(self.max_text_length)
                for content_type in parser.supported_types:
                    self._parsers[content_type] = parser
                logger.debug(f"Registered parser {parser_class.__name__} for types: {parser.supported_types}")
            except Exception as e:
                logger.warning(f"Failed to initialize parser {parser_class.__name__}: {e}")
    
    def get_parser(self, category: str) -> Optional[BaseParser]:
        """Get appropriate parser for content category.
        
        Args:
            category: Content category (e.g., 'ms_word', 'pdf', 'text')
            
        Returns:
            Parser instance or None if not supported
        """
        parser = self._parsers.get(category)
        if parser:
            logger.debug(f"Found parser {parser.__class__.__name__} for category: {category}")
        else:
            logger.warning(f"No parser available for category: {category}")
        return parser
    
    def parse_content(self, data: bytes, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Parse content using appropriate parser.
        
        Args:
            data: Raw content data
            metadata: Content metadata including 'category'
            
        Returns:
            Dictionary with parsed results
            
        Raises:
            ParsingError: If no parser available or parsing fails
        """
        category = metadata.get('category')
        if not category:
            raise ParsingError("No category specified in metadata")
        
        parser = self.get_parser(category)
        if not parser:
            raise ParsingError(f"No parser available for category: {category}")
        
        try:
            result = parser.parse(data, metadata)
            logger.info(f"Successfully parsed {category} content: {result['text_length']} characters extracted")
            return result
        except Exception as e:
            logger.error(f"Parsing failed for category {category}: {e}")
            # Return error result instead of raising
            return parser.handle_parsing_error(e, metadata)
    
    def get_supported_categories(self) -> list:
        """Get list of all supported content categories.
        
        Returns:
            List of supported category names
        """
        return list(self._parsers.keys())
    
    def is_category_supported(self, category: str) -> bool:
        """Check if a content category is supported.
        
        Args:
            category: Content category to check
            
        Returns:
            True if category is supported
        """
        return category in self._parsers
    
    def get_parser_info(self) -> Dict[str, Any]:
        """Get information about available parsers.
        
        Returns:
            Dictionary with parser information
        """
        info = {
            'total_parsers': len(set(self._parsers.values())),
            'supported_categories': self.get_supported_categories(),
            'parser_details': {}
        }
        
        # Get details for each unique parser
        seen_parsers = set()
        for category, parser in self._parsers.items():
            parser_name = parser.__class__.__name__
            if parser_name not in seen_parsers:
                info['parser_details'][parser_name] = {
                    'supported_types': parser.supported_types,
                    'max_text_length': parser.max_text_length
                }
                seen_parsers.add(parser_name)
        
        return info
