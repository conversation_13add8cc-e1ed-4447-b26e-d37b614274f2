2025-06-17 09:02:47 | INFO | src.logging_setup:setup_logging:46 | Logging initialized - Level: INFO, File: logs\niche_etl.log
2025-06-17 09:02:47 | INFO | __main__:cli:39 | Niche Text ETL starting with config: config.yaml
2025-06-17 09:02:47 | INFO | src.pipeline:__init__:108 | Pipeline initialized successfully
2025-06-17 09:02:47 | INFO | src.pipeline:_test_connections:175 | Testing database connections...
2025-06-17 09:03:02 | ERROR | src.database:get_connection:75 | Database connection error: ('08001', '[08001] [Microsoft][ODBC Driver 17 for SQL Server]Named Pipes Provider: Could not open a connection to SQL Server [53].  (53) (SQLDriverConnect); [08001] [Microsoft][ODBC Driver 17 for SQL Server]Login timeout expired (0); [08001] [Microsoft][ODBC Driver 17 for SQL Server]Invalid connection string attribute (0); [08001] [Microsoft][ODBC Driver 17 for SQL Server]A network-related or instance-specific error has occurred while establishing a connection to SQL Server. Server is not found or not accessible. Check if instance name is correct and if SQL Server is configured to allow remote connections. For more information see SQL Server Books Online. (53)')
2025-06-17 09:03:02 | ERROR | src.database:test_connection:93 | Connection test failed: ('08001', '[08001] [Microsoft][ODBC Driver 17 for SQL Server]Named Pipes Provider: Could not open a connection to SQL Server [53].  (53) (SQLDriverConnect); [08001] [Microsoft][ODBC Driver 17 for SQL Server]Login timeout expired (0); [08001] [Microsoft][ODBC Driver 17 for SQL Server]Invalid connection string attribute (0); [08001] [Microsoft][ODBC Driver 17 for SQL Server]A network-related or instance-specific error has occurred while establishing a connection to SQL Server. Server is not found or not accessible. Check if instance name is correct and if SQL Server is configured to allow remote connections. For more information see SQL Server Books Online. (53)')
2025-06-17 09:09:14 | INFO | src.logging_setup:setup_logging:46 | Logging initialized - Level: INFO, File: logs\niche_etl.log
2025-06-17 09:09:14 | INFO | __main__:cli:37 | Niche Text ETL starting with config: config.yaml
2025-06-17 09:10:01 | INFO | src.logging_setup:setup_logging:46 | Logging initialized - Level: INFO, File: logs\niche_etl.log
2025-06-17 09:10:01 | INFO | __main__:cli:37 | Niche Text ETL starting with config: config.yaml
2025-06-17 09:10:34 | INFO | src.logging_setup:setup_logging:46 | Logging initialized - Level: INFO, File: logs\niche_etl.log
2025-06-17 09:10:34 | INFO | __main__:cli:37 | Niche Text ETL starting with config: config.yaml
2025-06-17 09:11:00 | INFO | src.logging_setup:setup_logging:46 | Logging initialized - Level: INFO, File: logs\niche_etl.log
2025-06-17 09:11:00 | INFO | __main__:cli:37 | Niche Text ETL starting with config: config.yaml
2025-06-18 13:55:47 | INFO | src.logging_setup:setup_logging:46 | Logging initialized - Level: INFO, File: logs\niche_etl.log
2025-06-18 13:55:47 | INFO | __main__:main:160 | Niche Text ETL starting with simplified execution model
2025-06-18 13:55:47 | INFO | __main__:main:163 | Initializing pipeline...
2025-06-18 13:55:47 | INFO | src.config_validator:validate_all:55 | Starting comprehensive configuration validation...
2025-06-18 13:55:47 | WARNING | src.config_validator:add_warning:22 | Configuration warning: Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed
2025-06-18 13:55:47 | INFO | src.config_validator:set_module_flag:34 | Module 'email_enabled' is disabled
2025-06-18 13:55:59 | INFO | src.config_validator:_validate_embedding_module:169 | Text embedding model accessibility test successful
2025-06-18 13:55:59 | INFO | src.config_validator:set_module_flag:34 | Module 'embedding_enabled' is enabled
2025-06-18 13:55:59 | INFO | src.config_validator:_log_validation_summary:240 | Configuration validation completed
2025-06-18 13:55:59 | INFO | src.config_validator:_log_validation_summary:241 | Overall validation status: PASSED
2025-06-18 13:55:59 | INFO | src.config_validator:_log_validation_summary:242 | Warnings: 1
2025-06-18 13:55:59 | INFO | src.config_validator:_log_validation_summary:243 | Errors: 0
2025-06-18 13:55:59 | INFO | src.config_validator:_log_validation_summary:246 | Module status:
2025-06-18 13:55:59 | INFO | src.config_validator:_log_validation_summary:249 |   - email_enabled: DISABLED
2025-06-18 13:55:59 | INFO | src.config_validator:_log_validation_summary:249 |   - embedding_enabled: ENABLED
2025-06-18 13:55:59 | WARNING | src.config_validator:_log_validation_summary:252 | Configuration warnings found:
2025-06-18 13:55:59 | WARNING | src.config_validator:_log_validation_summary:254 |   - Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed
2025-06-18 13:55:59 | INFO | src.embedding.factory:create_provider:46 | Created embedding provider: sentence_transformers
2025-06-18 13:55:59 | INFO | src.embedding.processor:_initialize_provider:45 | Initialized embedding provider: sentence_transformers
2025-06-18 13:55:59 | INFO | src.pipeline:__init__:120 | Text embedding processor initialized
2025-06-18 13:55:59 | INFO | src.pipeline:__init__:125 | Pipeline initialized successfully
2025-06-18 13:55:59 | INFO | __main__:main:169 | Validating configuration...
2025-06-18 13:55:59 | INFO | src.pipeline:_test_connections:220 | Testing database connections...
2025-06-18 13:55:59 | ERROR | src.database:get_connection:87 | Database connection error: ('28000', "[28000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Login failed for user 'HRPS\\HRPS30783'. (18456) (SQLDriverConnect); [28000] [Microsoft][ODBC Driver 17 for SQL Server]Invalid connection string attribute (0); [28000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Login failed for user 'HRPS\\HRPS30783'. (18456); [28000] [Microsoft][ODBC Driver 17 for SQL Server]Invalid connection string attribute (0)")
2025-06-18 13:55:59 | ERROR | src.database:test_connection:105 | Connection test failed: ('28000', "[28000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Login failed for user 'HRPS\\HRPS30783'. (18456) (SQLDriverConnect); [28000] [Microsoft][ODBC Driver 17 for SQL Server]Invalid connection string attribute (0); [28000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Login failed for user 'HRPS\\HRPS30783'. (18456); [28000] [Microsoft][ODBC Driver 17 for SQL Server]Invalid connection string attribute (0)")
2025-06-18 13:55:59 | ERROR | __main__:main:176 | Configuration validation failed:
2025-06-18 13:55:59 | ERROR | __main__:main:178 |   - Database connection error: Source database connection failed
2025-06-18 13:55:59 | ERROR | __main__:main:225 | Configuration Error: Configuration validation failed
2025-06-18 13:57:09 | INFO | src.logging_setup:setup_logging:46 | Logging initialized - Level: INFO, File: logs\niche_etl.log
2025-06-18 13:57:09 | INFO | __main__:main:160 | Niche Text ETL starting with simplified execution model
2025-06-18 13:57:09 | INFO | __main__:main:163 | Initializing pipeline...
2025-06-18 13:57:09 | INFO | src.config_validator:validate_all:55 | Starting comprehensive configuration validation...
2025-06-18 13:57:09 | WARNING | src.config_validator:add_warning:22 | Configuration warning: Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed
2025-06-18 13:57:09 | INFO | src.config_validator:set_module_flag:34 | Module 'email_enabled' is disabled
2025-06-18 13:57:20 | INFO | src.config_validator:_validate_embedding_module:169 | Text embedding model accessibility test successful
2025-06-18 13:57:20 | INFO | src.config_validator:set_module_flag:34 | Module 'embedding_enabled' is enabled
2025-06-18 13:57:20 | INFO | src.config_validator:_log_validation_summary:240 | Configuration validation completed
2025-06-18 13:57:20 | INFO | src.config_validator:_log_validation_summary:241 | Overall validation status: PASSED
2025-06-18 13:57:20 | INFO | src.config_validator:_log_validation_summary:242 | Warnings: 1
2025-06-18 13:57:20 | INFO | src.config_validator:_log_validation_summary:243 | Errors: 0
2025-06-18 13:57:20 | INFO | src.config_validator:_log_validation_summary:246 | Module status:
2025-06-18 13:57:20 | INFO | src.config_validator:_log_validation_summary:249 |   - email_enabled: DISABLED
2025-06-18 13:57:20 | INFO | src.config_validator:_log_validation_summary:249 |   - embedding_enabled: ENABLED
2025-06-18 13:57:20 | WARNING | src.config_validator:_log_validation_summary:252 | Configuration warnings found:
2025-06-18 13:57:20 | WARNING | src.config_validator:_log_validation_summary:254 |   - Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed
2025-06-18 13:57:20 | INFO | src.embedding.factory:create_provider:46 | Created embedding provider: sentence_transformers
2025-06-18 13:57:20 | INFO | src.embedding.processor:_initialize_provider:45 | Initialized embedding provider: sentence_transformers
2025-06-18 13:57:20 | INFO | src.pipeline:__init__:120 | Text embedding processor initialized
2025-06-18 13:57:20 | INFO | src.pipeline:__init__:125 | Pipeline initialized successfully
2025-06-18 13:57:20 | INFO | __main__:main:169 | Validating configuration...
2025-06-18 13:57:20 | INFO | src.pipeline:_test_connections:220 | Testing database connections...
2025-06-18 13:57:21 | INFO | src.pipeline:_test_connections:228 | Database connections successful
2025-06-18 13:57:21 | WARNING | __main__:main:186 | Configuration warnings:
2025-06-18 13:57:21 | WARNING | __main__:main:188 |   - Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed
2025-06-18 13:57:21 | INFO | __main__:main:190 | Configuration validation completed
2025-06-18 13:57:21 | INFO | __main__:main:199 | Starting ETL pipeline execution...
2025-06-18 13:57:21 | INFO | src.pipeline:run:133 | Starting Niche Text ETL Pipeline
2025-06-18 13:57:21 | INFO | src.pipeline:_test_connections:220 | Testing database connections...
2025-06-18 13:57:21 | INFO | src.pipeline:_test_connections:228 | Database connections successful
2025-06-18 13:57:21 | ERROR | src.database:get_connection:87 | Database connection error: ('42S02', "[42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid object name 'NicheRMSReport.dbo.NicheBlobETLCheckpoints'. (208) (SQLExecDirectW); [42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)")
2025-06-18 13:57:21 | WARNING | src.database:get_last_processed_id:126 | Could not retrieve last processed ID for niche_text_etl: ('42S02', "[42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid object name 'NicheRMSReport.dbo.NicheBlobETLCheckpoints'. (208) (SQLExecDirectW); [42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)"). Starting from 0.
2025-06-18 13:57:21 | ERROR | src.database:get_connection:87 | Database connection error: ('42S22', "[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)")
2025-06-18 13:57:21 | ERROR | src.database:fetch_batch:248 | Error fetching batch: ('42S22', "[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)")
2025-06-18 13:57:21 | ERROR | src.pipeline:run:215 | Pipeline failed: ('42S22', "[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)")
2025-06-18 13:57:21 | ERROR | __main__:main:238 | Unexpected error: ('42S22', "[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)")
2025-06-18 14:07:59 | INFO | src.logging_setup:setup_logging:46 | Logging initialized - Level: INFO, File: logs\niche_etl.log
2025-06-18 14:07:59 | INFO | __main__:main:160 | Niche Text ETL starting with simplified execution model
2025-06-18 14:07:59 | INFO | __main__:main:163 | Initializing pipeline...
2025-06-18 14:07:59 | INFO | src.config_validator:validate_all:55 | Starting comprehensive configuration validation...
2025-06-18 14:07:59 | WARNING | src.config_validator:add_warning:22 | Configuration warning: Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed
2025-06-18 14:07:59 | INFO | src.config_validator:set_module_flag:34 | Module 'email_enabled' is disabled
2025-06-18 14:08:13 | INFO | src.config_validator:_validate_embedding_module:169 | Text embedding model accessibility test successful
2025-06-18 14:08:13 | INFO | src.config_validator:set_module_flag:34 | Module 'embedding_enabled' is enabled
2025-06-18 14:08:13 | INFO | src.config_validator:_log_validation_summary:240 | Configuration validation completed
2025-06-18 14:08:13 | INFO | src.config_validator:_log_validation_summary:241 | Overall validation status: PASSED
2025-06-18 14:08:13 | INFO | src.config_validator:_log_validation_summary:242 | Warnings: 1
2025-06-18 14:08:13 | INFO | src.config_validator:_log_validation_summary:243 | Errors: 0
2025-06-18 14:08:13 | INFO | src.config_validator:_log_validation_summary:246 | Module status:
2025-06-18 14:08:13 | INFO | src.config_validator:_log_validation_summary:249 |   - email_enabled: DISABLED
2025-06-18 14:08:13 | INFO | src.config_validator:_log_validation_summary:249 |   - embedding_enabled: ENABLED
2025-06-18 14:08:13 | WARNING | src.config_validator:_log_validation_summary:252 | Configuration warnings found:
2025-06-18 14:08:13 | WARNING | src.config_validator:_log_validation_summary:254 |   - Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed
2025-06-18 14:08:13 | INFO | src.embedding.factory:create_provider:46 | Created embedding provider: sentence_transformers
2025-06-18 14:08:13 | INFO | src.embedding.processor:_initialize_provider:45 | Initialized embedding provider: sentence_transformers
2025-06-18 14:08:13 | INFO | src.pipeline:__init__:120 | Text embedding processor initialized
2025-06-18 14:08:13 | INFO | src.pipeline:__init__:125 | Pipeline initialized successfully
2025-06-18 14:08:13 | INFO | __main__:main:169 | Validating configuration...
2025-06-18 14:08:13 | INFO | src.pipeline:_test_connections:220 | Testing database connections...
2025-06-18 14:08:13 | INFO | src.pipeline:_test_connections:228 | Database connections successful
2025-06-18 14:08:13 | WARNING | __main__:main:186 | Configuration warnings:
2025-06-18 14:08:13 | WARNING | __main__:main:188 |   - Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed
2025-06-18 14:08:13 | INFO | __main__:main:190 | Configuration validation completed
2025-06-18 14:08:13 | INFO | __main__:setup_database_if_needed:70 | Auto-setting up database...
2025-06-18 14:08:13 | ERROR | src.database:get_connection:87 | Database connection error: ('42000', "[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStaging' contains more than the maximum number of prefixes. The maximum is 2. (117) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLCheckpoints' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStatistics' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLEmbeddings' contains more than the maximum number of prefixes. The maximum is 2. (117)")
2025-06-18 14:08:13 | ERROR | src.database:execute_script:407 | Failed to execute SQL script: ('42000', "[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStaging' contains more than the maximum number of prefixes. The maximum is 2. (117) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLCheckpoints' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStatistics' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLEmbeddings' contains more than the maximum number of prefixes. The maximum is 2. (117)")
2025-06-18 14:08:13 | ERROR | __main__:setup_database_if_needed:101 | Database setup failed: ('42000', "[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStaging' contains more than the maximum number of prefixes. The maximum is 2. (117) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLCheckpoints' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStatistics' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLEmbeddings' contains more than the maximum number of prefixes. The maximum is 2. (117)")
2025-06-18 14:08:13 | ERROR | __main__:main:232 | Pipeline Error: Database setup failed: ('42000', "[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStaging' contains more than the maximum number of prefixes. The maximum is 2. (117) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLCheckpoints' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStatistics' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLEmbeddings' contains more than the maximum number of prefixes. The maximum is 2. (117)")
2025-06-18 14:08:26 | INFO | src.logging_setup:setup_logging:46 | Logging initialized - Level: INFO, File: logs\niche_etl.log
2025-06-18 14:08:26 | INFO | __main__:main:160 | Niche Text ETL starting with simplified execution model
2025-06-18 14:08:26 | INFO | __main__:main:163 | Initializing pipeline...
2025-06-18 14:08:26 | INFO | src.config_validator:validate_all:55 | Starting comprehensive configuration validation...
2025-06-18 14:08:26 | WARNING | src.config_validator:add_warning:22 | Configuration warning: Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed
2025-06-18 14:08:26 | INFO | src.config_validator:set_module_flag:34 | Module 'email_enabled' is disabled
2025-06-18 14:08:36 | INFO | src.config_validator:_validate_embedding_module:169 | Text embedding model accessibility test successful
2025-06-18 14:08:36 | INFO | src.config_validator:set_module_flag:34 | Module 'embedding_enabled' is enabled
2025-06-18 14:08:36 | INFO | src.config_validator:_log_validation_summary:240 | Configuration validation completed
2025-06-18 14:08:36 | INFO | src.config_validator:_log_validation_summary:241 | Overall validation status: PASSED
2025-06-18 14:08:36 | INFO | src.config_validator:_log_validation_summary:242 | Warnings: 1
2025-06-18 14:08:36 | INFO | src.config_validator:_log_validation_summary:243 | Errors: 0
2025-06-18 14:08:36 | INFO | src.config_validator:_log_validation_summary:246 | Module status:
2025-06-18 14:08:36 | INFO | src.config_validator:_log_validation_summary:249 |   - email_enabled: DISABLED
2025-06-18 14:08:36 | INFO | src.config_validator:_log_validation_summary:249 |   - embedding_enabled: ENABLED
2025-06-18 14:08:36 | WARNING | src.config_validator:_log_validation_summary:252 | Configuration warnings found:
2025-06-18 14:08:36 | WARNING | src.config_validator:_log_validation_summary:254 |   - Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed
2025-06-18 14:08:36 | INFO | src.embedding.factory:create_provider:46 | Created embedding provider: sentence_transformers
2025-06-18 14:08:36 | INFO | src.embedding.processor:_initialize_provider:45 | Initialized embedding provider: sentence_transformers
2025-06-18 14:08:36 | INFO | src.pipeline:__init__:120 | Text embedding processor initialized
2025-06-18 14:08:36 | INFO | src.pipeline:__init__:125 | Pipeline initialized successfully
2025-06-18 14:08:36 | INFO | __main__:main:169 | Validating configuration...
2025-06-18 14:08:36 | INFO | src.pipeline:_test_connections:220 | Testing database connections...
2025-06-18 14:08:36 | INFO | src.pipeline:_test_connections:228 | Database connections successful
2025-06-18 14:08:36 | WARNING | __main__:main:186 | Configuration warnings:
2025-06-18 14:08:36 | WARNING | __main__:main:188 |   - Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed
2025-06-18 14:08:36 | INFO | __main__:main:190 | Configuration validation completed
2025-06-18 14:08:36 | INFO | __main__:setup_database_if_needed:70 | Auto-setting up database...
2025-06-18 14:08:36 | ERROR | src.database:get_connection:87 | Database connection error: ('42000', "[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStaging' contains more than the maximum number of prefixes. The maximum is 2. (117) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLCheckpoints' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStatistics' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLEmbeddings' contains more than the maximum number of prefixes. The maximum is 2. (117)")
2025-06-18 14:08:36 | ERROR | src.database:execute_script:407 | Failed to execute SQL script: ('42000', "[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStaging' contains more than the maximum number of prefixes. The maximum is 2. (117) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLCheckpoints' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStatistics' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLEmbeddings' contains more than the maximum number of prefixes. The maximum is 2. (117)")
2025-06-18 14:08:36 | ERROR | __main__:setup_database_if_needed:101 | Database setup failed: ('42000', "[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStaging' contains more than the maximum number of prefixes. The maximum is 2. (117) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLCheckpoints' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStatistics' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLEmbeddings' contains more than the maximum number of prefixes. The maximum is 2. (117)")
2025-06-18 14:08:36 | ERROR | __main__:main:232 | Pipeline Error: Database setup failed: ('42000', "[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStaging' contains more than the maximum number of prefixes. The maximum is 2. (117) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLCheckpoints' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStatistics' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLEmbeddings' contains more than the maximum number of prefixes. The maximum is 2. (117)")
2025-06-18 14:10:31 | INFO | src.logging_setup:setup_logging:46 | Logging initialized - Level: INFO, File: logs\niche_etl.log
2025-06-18 14:10:31 | INFO | __main__:main:160 | Niche Text ETL starting with simplified execution model
2025-06-18 14:10:31 | INFO | __main__:main:163 | Initializing pipeline...
2025-06-18 14:10:31 | INFO | src.config_validator:validate_all:55 | Starting comprehensive configuration validation...
2025-06-18 14:10:31 | WARNING | src.config_validator:add_warning:22 | Configuration warning: Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed
2025-06-18 14:10:31 | INFO | src.config_validator:set_module_flag:34 | Module 'email_enabled' is disabled
2025-06-18 14:10:42 | INFO | src.config_validator:_validate_embedding_module:169 | Text embedding model accessibility test successful
2025-06-18 14:10:42 | INFO | src.config_validator:set_module_flag:34 | Module 'embedding_enabled' is enabled
2025-06-18 14:10:42 | INFO | src.config_validator:_log_validation_summary:240 | Configuration validation completed
2025-06-18 14:10:42 | INFO | src.config_validator:_log_validation_summary:241 | Overall validation status: PASSED
2025-06-18 14:10:42 | INFO | src.config_validator:_log_validation_summary:242 | Warnings: 1
2025-06-18 14:10:42 | INFO | src.config_validator:_log_validation_summary:243 | Errors: 0
2025-06-18 14:10:42 | INFO | src.config_validator:_log_validation_summary:246 | Module status:
2025-06-18 14:10:42 | INFO | src.config_validator:_log_validation_summary:249 |   - email_enabled: DISABLED
2025-06-18 14:10:42 | INFO | src.config_validator:_log_validation_summary:249 |   - embedding_enabled: ENABLED
2025-06-18 14:10:42 | WARNING | src.config_validator:_log_validation_summary:252 | Configuration warnings found:
2025-06-18 14:10:42 | WARNING | src.config_validator:_log_validation_summary:254 |   - Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed
2025-06-18 14:10:42 | INFO | src.embedding.factory:create_provider:46 | Created embedding provider: sentence_transformers
2025-06-18 14:10:42 | INFO | src.embedding.processor:_initialize_provider:45 | Initialized embedding provider: sentence_transformers
2025-06-18 14:10:42 | INFO | src.pipeline:__init__:120 | Text embedding processor initialized
2025-06-18 14:10:42 | INFO | src.pipeline:__init__:125 | Pipeline initialized successfully
2025-06-18 14:10:42 | INFO | __main__:main:169 | Validating configuration...
2025-06-18 14:10:42 | INFO | src.pipeline:_test_connections:220 | Testing database connections...
2025-06-18 14:10:42 | INFO | src.pipeline:_test_connections:228 | Database connections successful
2025-06-18 14:10:42 | WARNING | __main__:main:186 | Configuration warnings:
2025-06-18 14:10:42 | WARNING | __main__:main:188 |   - Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed
2025-06-18 14:10:42 | INFO | __main__:main:190 | Configuration validation completed
2025-06-18 14:10:42 | INFO | __main__:setup_database_if_needed:70 | Auto-setting up database...
2025-06-18 14:10:42 | INFO | src.database:execute_script:405 | SQL script executed successfully.
2025-06-18 14:10:42 | INFO | __main__:setup_database_if_needed:98 | Database setup completed successfully
2025-06-18 14:10:42 | INFO | __main__:main:199 | Starting ETL pipeline execution...
2025-06-18 14:10:42 | INFO | src.pipeline:run:133 | Starting Niche Text ETL Pipeline
2025-06-18 14:10:42 | INFO | src.pipeline:_test_connections:220 | Testing database connections...
2025-06-18 14:10:42 | INFO | src.pipeline:_test_connections:228 | Database connections successful
2025-06-18 14:10:42 | ERROR | src.database:get_connection:87 | Database connection error: ('42S02', "[42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid object name 'NicheRMSReport.dbo.NicheBlobETLCheckpoints'. (208) (SQLExecDirectW); [42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)")
2025-06-18 14:10:42 | WARNING | src.database:get_last_processed_id:126 | Could not retrieve last processed ID for niche_text_etl: ('42S02', "[42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid object name 'NicheRMSReport.dbo.NicheBlobETLCheckpoints'. (208) (SQLExecDirectW); [42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)"). Starting from 0.
2025-06-18 14:10:45 | INFO | src.database:fetch_batch:340 | Fetched batch of 1000 records (IDs: 10023001000000046127415 - 10023001000000046336135)
2025-06-18 14:10:45 | INFO | src.pipeline:run:166 | Trimming batch to 100 records to stay within debug limit
2025-06-18 14:10:45 | INFO | src.pipeline:run:172 | Processing batch 1 with 100 records (total: 100/100)
2025-06-18 14:10:45 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 1098 characters extracted
2025-06-18 14:10:45 | INFO | src.embedding.providers:_load_model:175 | Loading Sentence Transformers model: all-MiniLM-L6-v2
2025-06-18 14:10:45 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 666 characters extracted
2025-06-18 14:10:45 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 991 characters extracted
2025-06-18 14:10:45 | INFO | src.embedding.providers:_load_model:175 | Loading Sentence Transformers model: all-MiniLM-L6-v2
2025-06-18 14:10:45 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 1592 characters extracted
2025-06-18 14:10:45 | INFO | src.embedding.providers:_load_model:175 | Loading Sentence Transformers model: all-MiniLM-L6-v2
2025-06-18 14:10:45 | INFO | src.embedding.providers:_load_model:175 | Loading Sentence Transformers model: all-MiniLM-L6-v2
2025-06-18 14:10:46 | ERROR | src.embedding.processor:process_text:93 | Embedding processing failed for record 10023001000000046128477: Single text embedding failed: Failed to generate embedding: Failed to load model all-MiniLM-L6-v2: Cannot copy out of meta tensor; no data! Please use torch.nn.Module.to_empty() instead of torch.nn.Module.to() when moving module from meta to a different device.
2025-06-18 14:10:46 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 785 characters extracted
2025-06-18 14:10:46 | INFO | src.embedding.providers:_load_model:175 | Loading Sentence Transformers model: all-MiniLM-L6-v2
2025-06-18 14:10:46 | INFO | src.embedding.providers:_load_model:178 | Successfully loaded model: all-MiniLM-L6-v2
2025-06-18 14:10:46 | INFO | src.embedding.providers:_load_model:178 | Successfully loaded model: all-MiniLM-L6-v2
2025-06-18 14:10:46 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 670 characters extracted
2025-06-18 14:10:46 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 453 characters extracted
2025-06-18 14:10:46 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 341 characters extracted
2025-06-18 14:10:46 | INFO | src.embedding.providers:_load_model:178 | Successfully loaded model: all-MiniLM-L6-v2
2025-06-18 14:10:46 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 933 characters extracted
2025-06-18 14:10:46 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 3123 characters extracted
2025-06-18 14:10:46 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 428 characters extracted
2025-06-18 14:10:46 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 396 characters extracted
2025-06-18 14:10:46 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 1071 characters extracted
2025-06-18 14:10:46 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 349 characters extracted
2025-06-18 14:10:46 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 245 characters extracted
2025-06-18 14:10:46 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 287 characters extracted
2025-06-18 14:10:46 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 944 characters extracted
2025-06-18 14:10:47 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 900 characters extracted
2025-06-18 14:10:47 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 1028 characters extracted
2025-06-18 14:10:47 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 301 characters extracted
2025-06-18 14:10:47 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 713 characters extracted
2025-06-18 14:10:47 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 161 characters extracted
2025-06-18 14:10:47 | INFO | src.embedding.providers:_load_model:178 | Successfully loaded model: all-MiniLM-L6-v2
2025-06-18 14:10:47 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 753 characters extracted
2025-06-18 14:10:47 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 858 characters extracted
2025-06-18 14:10:47 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 896 characters extracted
2025-06-18 14:10:47 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 151 characters extracted
2025-06-18 14:10:47 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 825 characters extracted
2025-06-18 14:10:47 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 388 characters extracted
2025-06-18 14:10:47 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 294 characters extracted
2025-06-18 14:10:47 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 273 characters extracted
2025-06-18 14:10:47 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 665 characters extracted
2025-06-18 14:10:47 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 483 characters extracted
2025-06-18 14:10:47 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 359 characters extracted
2025-06-18 14:10:47 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 868 characters extracted
2025-06-18 14:10:47 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 551 characters extracted
2025-06-18 14:10:47 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 222 characters extracted
2025-06-18 14:10:47 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 299 characters extracted
2025-06-18 14:10:47 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 36 characters extracted
2025-06-18 14:10:47 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 553 characters extracted
2025-06-18 14:10:47 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 291 characters extracted
2025-06-18 14:10:47 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 36 characters extracted
2025-06-18 14:10:47 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 36 characters extracted
2025-06-18 14:10:47 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 304 characters extracted
2025-06-18 14:10:47 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 282 characters extracted
2025-06-18 14:10:47 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 423 characters extracted
2025-06-18 14:10:47 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 194 characters extracted
2025-06-18 14:10:47 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 349 characters extracted
2025-06-18 14:10:47 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 244 characters extracted
2025-06-18 14:10:47 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 157 characters extracted
2025-06-18 14:10:47 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 458 characters extracted
2025-06-18 14:10:47 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 332 characters extracted
2025-06-18 14:10:47 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 107 characters extracted
2025-06-18 14:10:47 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 372 characters extracted
2025-06-18 14:10:47 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 424 characters extracted
2025-06-18 14:10:48 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 382 characters extracted
2025-06-18 14:10:48 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 393 characters extracted
2025-06-18 14:10:48 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 246 characters extracted
2025-06-18 14:10:48 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 393 characters extracted
2025-06-18 14:10:48 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 280 characters extracted
2025-06-18 14:10:48 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 326 characters extracted
2025-06-18 14:10:48 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 196 characters extracted
2025-06-18 14:10:48 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 354 characters extracted
2025-06-18 14:10:48 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 290 characters extracted
2025-06-18 14:10:48 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 363 characters extracted
2025-06-18 14:10:48 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 308 characters extracted
2025-06-18 14:10:48 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 450 characters extracted
2025-06-18 14:10:48 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 370 characters extracted
2025-06-18 14:10:48 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 297 characters extracted
2025-06-18 14:10:48 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 346 characters extracted
2025-06-18 14:10:48 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 197 characters extracted
2025-06-18 14:10:48 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 395 characters extracted
2025-06-18 14:10:48 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 404 characters extracted
2025-06-18 14:10:48 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 963 characters extracted
2025-06-18 14:10:48 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 241 characters extracted
2025-06-18 14:10:48 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 393 characters extracted
2025-06-18 14:10:48 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 453 characters extracted
2025-06-18 14:10:48 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 867 characters extracted
2025-06-18 14:10:48 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 798 characters extracted
2025-06-18 14:10:48 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 699 characters extracted
2025-06-18 14:10:48 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 771 characters extracted
2025-06-18 14:10:48 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 728 characters extracted
2025-06-18 14:10:48 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 541 characters extracted
2025-06-18 14:10:49 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 355 characters extracted
2025-06-18 14:10:49 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 377 characters extracted
2025-06-18 14:10:49 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 651 characters extracted
2025-06-18 14:10:49 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 871 characters extracted
2025-06-18 14:10:49 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 604 characters extracted
2025-06-18 14:10:49 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 730 characters extracted
2025-06-18 14:10:49 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 707 characters extracted
2025-06-18 14:10:49 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 391 characters extracted
2025-06-18 14:10:49 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 397 characters extracted
2025-06-18 14:10:49 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 493 characters extracted
2025-06-18 14:10:49 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 481 characters extracted
2025-06-18 14:10:49 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 270 characters extracted
2025-06-18 14:10:49 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 821 characters extracted
2025-06-18 14:10:49 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 441 characters extracted
2025-06-18 14:10:49 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 500 characters extracted
2025-06-18 14:10:49 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 868 characters extracted
2025-06-18 14:10:49 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 793 characters extracted
2025-06-18 14:10:49 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 931 characters extracted
2025-06-18 14:10:49 | ERROR | src.database:get_connection:87 | Database connection error: ('22003', '[22003] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Arithmetic overflow error converting expression to data type bigint. (8115) (SQLExecDirectW); [22003] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The statement has been terminated. (3621)')
2025-06-18 14:10:49 | ERROR | src.database:insert_batch:390 | Error inserting batch into NicheBlobETLStaging: ('22003', '[22003] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Arithmetic overflow error converting expression to data type bigint. (8115) (SQLExecDirectW); [22003] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The statement has been terminated. (3621)')
2025-06-18 14:10:49 | INFO | src.pipeline:run:204 | Reached debug limit of 100 records. Processing complete.
2025-06-18 14:10:49 | INFO | src.pipeline:run:209 | Pipeline completed successfully: {'records_processed': 100, 'records_successful': 100, 'records_failed': 0, 'success_rate': 100.0, 'bytes_processed': 45337, 'text_extracted': 53083, 'duration_seconds': 7.185889, 'records_per_second': 13.916162634852833, 'error_count': 0, 'recent_errors': []}
2025-06-18 14:10:49 | INFO | __main__:main:203 | Pipeline execution completed successfully
2025-06-18 14:10:49 | INFO | __main__:main:204 | Final statistics: {
  "records_processed": 100,
  "records_successful": 100,
  "records_failed": 0,
  "success_rate": 100.0,
  "bytes_processed": 45337,
  "text_extracted": 53083,
  "duration_seconds": 7.185889,
  "records_per_second": 13.916162634852833,
  "error_count": 0,
  "recent_errors": []
}
2025-06-18 14:10:49 | INFO | __main__:run_monitoring_if_enabled:115 | Running daily monitoring tasks...
2025-06-18 14:10:49 | INFO | src.monitoring:run_daily_monitoring:461 | Starting daily monitoring tasks
2025-06-18 14:10:49 | INFO | src.monitoring:run_daily_monitoring:472 | Analyzing logs for the past 24 hours
2025-06-18 14:10:49 | INFO | src.monitoring:run_daily_monitoring:476 | Checking if alert should be sent
2025-06-18 14:10:49 | INFO | src.monitoring:send_alert:226 | Email alerts disabled - skipping alert
2025-06-18 14:10:49 | INFO | src.monitoring:run_daily_monitoring:480 | Rotating old log files
2025-06-18 14:10:49 | INFO | src.monitoring:rotate_logs:429 | Log rotation complete: 0 deleted, 1 kept, 0 bytes freed
2025-06-18 14:10:49 | INFO | src.monitoring:run_daily_monitoring:483 | Daily monitoring tasks completed successfully
2025-06-18 14:10:49 | INFO | __main__:run_monitoring_if_enabled:119 | Monitoring completed: {
  "timestamp": "2025-06-18 14:10:49.919813",
  "log_analysis": {
    "period_start": "2025-06-17 14:10:49.920812",
    "period_end": "2025-06-18 14:10:49.920812",
    "errors": [
      {
        "file": "logs\\niche_etl.log",
        "line": 38,
        "timestamp": "2025-06-18 13:55:59",
        "message": "Database connection error: ('28000', \"[28000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Login failed for user 'HRPS\\\\HRPS30783'. (18456) (SQLDriverConnect); [28000] [Microsoft][ODBC Driver 17 for SQL Server]Invalid connection string attribute (0); [28000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Login failed for user 'HRPS\\\\HRPS30783'. (18456); [28000] [Microsoft][ODBC Driver 17 for SQL Server]Invalid connection string attribute (0)\")",
        "context": "src.database:get_connection:87"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 39,
        "timestamp": "2025-06-18 13:55:59",
        "message": "Connection test failed: ('28000', \"[28000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Login failed for user 'HRPS\\\\HRPS30783'. (18456) (SQLDriverConnect); [28000] [Microsoft][ODBC Driver 17 for SQL Server]Invalid connection string attribute (0); [28000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Login failed for user 'HRPS\\\\HRPS30783'. (18456); [28000] [Microsoft][ODBC Driver 17 for SQL Server]Invalid connection string attribute (0)\")",
        "context": "src.database:test_connection:105"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 40,
        "timestamp": "2025-06-18 13:55:59",
        "message": "Configuration validation failed:",
        "context": "__main__:main:176"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 41,
        "timestamp": "2025-06-18 13:55:59",
        "message": "- Database connection error: Source database connection failed",
        "context": "__main__:main:178"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 42,
        "timestamp": "2025-06-18 13:55:59",
        "message": "Configuration Error: Configuration validation failed",
        "context": "__main__:main:225"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 74,
        "timestamp": "2025-06-18 13:57:21",
        "message": "Database connection error: ('42S02', \"[42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid object name 'NicheRMSReport.dbo.NicheBlobETLCheckpoints'. (208) (SQLExecDirectW); [42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)\")",
        "context": "src.database:get_connection:87"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 76,
        "timestamp": "2025-06-18 13:57:21",
        "message": "Database connection error: ('42S22', \"[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)\")",
        "context": "src.database:get_connection:87"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 77,
        "timestamp": "2025-06-18 13:57:21",
        "message": "Error fetching batch: ('42S22', \"[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)\")",
        "context": "src.database:fetch_batch:248"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 78,
        "timestamp": "2025-06-18 13:57:21",
        "message": "Pipeline failed: ('42S22', \"[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)\")",
        "context": "src.pipeline:run:215"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 79,
        "timestamp": "2025-06-18 13:57:21",
        "message": "Unexpected error: ('42S22', \"[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)\")",
        "context": "__main__:main:238"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 108,
        "timestamp": "2025-06-18 14:08:13",
        "message": "Database connection error: ('42000', \"[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStaging' contains more than the maximum number of prefixes. The maximum is 2. (117) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLCheckpoints' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStatistics' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLEmbeddings' contains more than the maximum number of prefixes. The maximum is 2. (117)\")",
        "context": "src.database:get_connection:87"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 109,
        "timestamp": "2025-06-18 14:08:13",
        "message": "Failed to execute SQL script: ('42000', \"[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStaging' contains more than the maximum number of prefixes. The maximum is 2. (117) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLCheckpoints' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStatistics' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLEmbeddings' contains more than the maximum number of prefixes. The maximum is 2. (117)\")",
        "context": "src.database:execute_script:407"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 110,
        "timestamp": "2025-06-18 14:08:13",
        "message": "Database setup failed: ('42000', \"[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStaging' contains more than the maximum number of prefixes. The maximum is 2. (117) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLCheckpoints' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStatistics' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLEmbeddings' contains more than the maximum number of prefixes. The maximum is 2. (117)\")",
        "context": "__main__:setup_database_if_needed:101"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 111,
        "timestamp": "2025-06-18 14:08:13",
        "message": "Pipeline Error: Database setup failed: ('42000', \"[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStaging' contains more than the maximum number of prefixes. The maximum is 2. (117) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLCheckpoints' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStatistics' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLEmbeddings' contains more than the maximum number of prefixes. The maximum is 2. (117)\")",
        "context": "__main__:main:232"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 140,
        "timestamp": "2025-06-18 14:08:36",
        "message": "Database connection error: ('42000', \"[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStaging' contains more than the maximum number of prefixes. The maximum is 2. (117) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLCheckpoints' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStatistics' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLEmbeddings' contains more than the maximum number of prefixes. The maximum is 2. (117)\")",
        "context": "src.database:get_connection:87"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 141,
        "timestamp": "2025-06-18 14:08:36",
        "message": "Failed to execute SQL script: ('42000', \"[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStaging' contains more than the maximum number of prefixes. The maximum is 2. (117) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLCheckpoints' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStatistics' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLEmbeddings' contains more than the maximum number of prefixes. The maximum is 2. (117)\")",
        "context": "src.database:execute_script:407"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 142,
        "timestamp": "2025-06-18 14:08:36",
        "message": "Database setup failed: ('42000', \"[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStaging' contains more than the maximum number of prefixes. The maximum is 2. (117) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLCheckpoints' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStatistics' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLEmbeddings' contains more than the maximum number of prefixes. The maximum is 2. (117)\")",
        "context": "__main__:setup_database_if_needed:101"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 143,
        "timestamp": "2025-06-18 14:08:36",
        "message": "Pipeline Error: Database setup failed: ('42000', \"[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStaging' contains more than the maximum number of prefixes. The maximum is 2. (117) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLCheckpoints' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStatistics' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLEmbeddings' contains more than the maximum number of prefixes. The maximum is 2. (117)\")",
        "context": "__main__:main:232"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 178,
        "timestamp": "2025-06-18 14:10:42",
        "message": "Database connection error: ('42S02', \"[42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid object name 'NicheRMSReport.dbo.NicheBlobETLCheckpoints'. (208) (SQLExecDirectW); [42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)\")",
        "context": "src.database:get_connection:87"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 191,
        "timestamp": "2025-06-18 14:10:46",
        "message": "Embedding processing failed for record 10023001000000046128477: Single text embedding failed: Failed to generate embedding: Failed to load model all-MiniLM-L6-v2: Cannot copy out of meta tensor; no data! Please use torch.nn.Module.to_empty() instead of torch.nn.Module.to() when moving module from meta to a different device.",
        "context": "src.embedding.processor:process_text:93"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 293,
        "timestamp": "2025-06-18 14:10:49",
        "message": "Database connection error: ('22003', '[22003] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Arithmetic overflow error converting expression to data type bigint. (8115) (SQLExecDirectW); [22003] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The statement has been terminated. (3621)')",
        "context": "src.database:get_connection:87"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 294,
        "timestamp": "2025-06-18 14:10:49",
        "message": "Error inserting batch into NicheBlobETLStaging: ('22003', '[22003] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Arithmetic overflow error converting expression to data type bigint. (8115) (SQLExecDirectW); [22003] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The statement has been terminated. (3621)')",
        "context": "src.database:insert_batch:390"
      }
    ],
    "warnings": [
      {
        "file": "logs\\niche_etl.log",
        "line": 19,
        "timestamp": "2025-06-18 13:55:47",
        "message": "Configuration warning: Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "src.config_validator:add_warning:22"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 30,
        "timestamp": "2025-06-18 13:55:59",
        "message": "Configuration warnings found:",
        "context": "src.config_validator:_log_validation_summary:252"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 31,
        "timestamp": "2025-06-18 13:55:59",
        "message": "- Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "src.config_validator:_log_validation_summary:254"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 47,
        "timestamp": "2025-06-18 13:57:09",
        "message": "Configuration warning: Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "src.config_validator:add_warning:22"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 58,
        "timestamp": "2025-06-18 13:57:20",
        "message": "Configuration warnings found:",
        "context": "src.config_validator:_log_validation_summary:252"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 59,
        "timestamp": "2025-06-18 13:57:20",
        "message": "- Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "src.config_validator:_log_validation_summary:254"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 67,
        "timestamp": "2025-06-18 13:57:21",
        "message": "Configuration warnings:",
        "context": "__main__:main:186"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 68,
        "timestamp": "2025-06-18 13:57:21",
        "message": "- Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "__main__:main:188"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 75,
        "timestamp": "2025-06-18 13:57:21",
        "message": "Could not retrieve last processed ID for niche_text_etl: ('42S02', \"[42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid object name 'NicheRMSReport.dbo.NicheBlobETLCheckpoints'. (208) (SQLExecDirectW); [42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)\"). Starting from 0.",
        "context": "src.database:get_last_processed_id:126"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 84,
        "timestamp": "2025-06-18 14:07:59",
        "message": "Configuration warning: Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "src.config_validator:add_warning:22"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 95,
        "timestamp": "2025-06-18 14:08:13",
        "message": "Configuration warnings found:",
        "context": "src.config_validator:_log_validation_summary:252"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 96,
        "timestamp": "2025-06-18 14:08:13",
        "message": "- Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "src.config_validator:_log_validation_summary:254"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 104,
        "timestamp": "2025-06-18 14:08:13",
        "message": "Configuration warnings:",
        "context": "__main__:main:186"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 105,
        "timestamp": "2025-06-18 14:08:13",
        "message": "- Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "__main__:main:188"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 116,
        "timestamp": "2025-06-18 14:08:26",
        "message": "Configuration warning: Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "src.config_validator:add_warning:22"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 127,
        "timestamp": "2025-06-18 14:08:36",
        "message": "Configuration warnings found:",
        "context": "src.config_validator:_log_validation_summary:252"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 128,
        "timestamp": "2025-06-18 14:08:36",
        "message": "- Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "src.config_validator:_log_validation_summary:254"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 136,
        "timestamp": "2025-06-18 14:08:36",
        "message": "Configuration warnings:",
        "context": "__main__:main:186"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 137,
        "timestamp": "2025-06-18 14:08:36",
        "message": "- Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "__main__:main:188"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 148,
        "timestamp": "2025-06-18 14:10:31",
        "message": "Configuration warning: Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "src.config_validator:add_warning:22"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 159,
        "timestamp": "2025-06-18 14:10:42",
        "message": "Configuration warnings found:",
        "context": "src.config_validator:_log_validation_summary:252"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 160,
        "timestamp": "2025-06-18 14:10:42",
        "message": "- Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "src.config_validator:_log_validation_summary:254"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 168,
        "timestamp": "2025-06-18 14:10:42",
        "message": "Configuration warnings:",
        "context": "__main__:main:186"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 169,
        "timestamp": "2025-06-18 14:10:42",
        "message": "- Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "__main__:main:188"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 179,
        "timestamp": "2025-06-18 14:10:42",
        "message": "Could not retrieve last processed ID for niche_text_etl: ('42S02', \"[42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid object name 'NicheRMSReport.dbo.NicheBlobETLCheckpoints'. (208) (SQLExecDirectW); [42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)\"). Starting from 0.",
        "context": "src.database:get_last_processed_id:126"
      }
    ],
    "total_errors": 22,
    "total_warnings": 25,
    "files_analyzed": [
      "logs\\niche_etl.log"
    ],
    "summary": {
      "has_errors": true,
      "has_warnings": true,
      "error_categories": {
        "Database": 13,
        "Other": 9
      },
      "warning_categories": {
        "Database": 14,
        "Other": 11
      },
      "most_common_errors": [],
      "most_common_warnings": []
    }
  },
  "email_sent": true,
  "log_rotation": {
    "files_deleted": [],
    "files_kept": [
      "logs\\niche_etl.log"
    ],
    "total_deleted": 0,
    "total_kept": 1,
    "space_freed": 0
  }
}
2025-06-18 14:10:49 | INFO | __main__:main:215 | Niche Text ETL completed successfully
2025-06-18 14:13:39 | INFO | src.logging_setup:setup_logging:46 | Logging initialized - Level: INFO, File: logs\niche_etl.log
2025-06-18 14:13:39 | INFO | __main__:main:160 | Niche Text ETL starting with simplified execution model
2025-06-18 14:13:39 | INFO | __main__:main:163 | Initializing pipeline...
2025-06-18 14:13:39 | INFO | src.config_validator:validate_all:55 | Starting comprehensive configuration validation...
2025-06-18 14:13:39 | WARNING | src.config_validator:add_warning:22 | Configuration warning: Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed
2025-06-18 14:13:39 | INFO | src.config_validator:set_module_flag:34 | Module 'email_enabled' is disabled
2025-06-18 14:13:50 | INFO | src.config_validator:_validate_embedding_module:169 | Text embedding model accessibility test successful
2025-06-18 14:13:50 | INFO | src.config_validator:set_module_flag:34 | Module 'embedding_enabled' is enabled
2025-06-18 14:13:50 | INFO | src.config_validator:_log_validation_summary:240 | Configuration validation completed
2025-06-18 14:13:50 | INFO | src.config_validator:_log_validation_summary:241 | Overall validation status: PASSED
2025-06-18 14:13:50 | INFO | src.config_validator:_log_validation_summary:242 | Warnings: 1
2025-06-18 14:13:50 | INFO | src.config_validator:_log_validation_summary:243 | Errors: 0
2025-06-18 14:13:50 | INFO | src.config_validator:_log_validation_summary:246 | Module status:
2025-06-18 14:13:50 | INFO | src.config_validator:_log_validation_summary:249 |   - email_enabled: DISABLED
2025-06-18 14:13:50 | INFO | src.config_validator:_log_validation_summary:249 |   - embedding_enabled: ENABLED
2025-06-18 14:13:50 | WARNING | src.config_validator:_log_validation_summary:252 | Configuration warnings found:
2025-06-18 14:13:50 | WARNING | src.config_validator:_log_validation_summary:254 |   - Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed
2025-06-18 14:13:50 | INFO | src.embedding.factory:create_provider:46 | Created embedding provider: sentence_transformers
2025-06-18 14:13:50 | INFO | src.embedding.processor:_initialize_provider:45 | Initialized embedding provider: sentence_transformers
2025-06-18 14:13:50 | INFO | src.pipeline:__init__:120 | Text embedding processor initialized
2025-06-18 14:13:50 | INFO | src.pipeline:__init__:125 | Pipeline initialized successfully
2025-06-18 14:13:50 | INFO | __main__:main:169 | Validating configuration...
2025-06-18 14:13:50 | INFO | src.pipeline:_test_connections:220 | Testing database connections...
2025-06-18 14:13:50 | INFO | src.pipeline:_test_connections:228 | Database connections successful
2025-06-18 14:13:50 | WARNING | __main__:main:186 | Configuration warnings:
2025-06-18 14:13:50 | WARNING | __main__:main:188 |   - Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed
2025-06-18 14:13:50 | INFO | __main__:main:190 | Configuration validation completed
2025-06-18 14:13:50 | INFO | __main__:setup_database_if_needed:70 | Auto-setting up database...
2025-06-18 14:13:50 | INFO | src.database:execute_script:405 | SQL script executed successfully.
2025-06-18 14:13:50 | INFO | __main__:setup_database_if_needed:98 | Database setup completed successfully
2025-06-18 14:13:50 | INFO | __main__:main:199 | Starting ETL pipeline execution...
2025-06-18 14:13:50 | INFO | src.pipeline:run:133 | Starting Niche Text ETL Pipeline
2025-06-18 14:13:50 | INFO | src.pipeline:_test_connections:220 | Testing database connections...
2025-06-18 14:13:50 | INFO | src.pipeline:_test_connections:228 | Database connections successful
2025-06-18 14:13:50 | ERROR | src.database:get_connection:87 | Database connection error: ('42S02', "[42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid object name 'NicheRMSReport.dbo.NicheBlobETLCheckpoints'. (208) (SQLExecDirectW); [42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)")
2025-06-18 14:13:50 | WARNING | src.database:get_last_processed_id:126 | Could not retrieve last processed ID for niche_text_etl: ('42S02', "[42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid object name 'NicheRMSReport.dbo.NicheBlobETLCheckpoints'. (208) (SQLExecDirectW); [42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)"). Starting from 0.
2025-06-18 14:13:52 | INFO | src.database:fetch_batch:340 | Fetched batch of 1000 records (IDs: 10023001000000046127415 - 10023001000000046336135)
2025-06-18 14:13:52 | INFO | src.pipeline:run:166 | Trimming batch to 100 records to stay within debug limit
2025-06-18 14:13:52 | INFO | src.pipeline:run:172 | Processing batch 1 with 100 records (total: 100/100)
2025-06-18 14:13:52 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 1098 characters extracted
2025-06-18 14:13:52 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 666 characters extracted
2025-06-18 14:13:52 | INFO | src.embedding.providers:_load_model:175 | Loading Sentence Transformers model: all-MiniLM-L6-v2
2025-06-18 14:13:52 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 991 characters extracted
2025-06-18 14:13:52 | INFO | src.embedding.providers:_load_model:175 | Loading Sentence Transformers model: all-MiniLM-L6-v2
2025-06-18 14:13:52 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 1592 characters extracted
2025-06-18 14:13:52 | INFO | src.embedding.providers:_load_model:175 | Loading Sentence Transformers model: all-MiniLM-L6-v2
2025-06-18 14:13:52 | INFO | src.embedding.providers:_load_model:175 | Loading Sentence Transformers model: all-MiniLM-L6-v2
2025-06-18 14:13:53 | INFO | src.embedding.providers:_load_model:178 | Successfully loaded model: all-MiniLM-L6-v2
2025-06-18 14:13:53 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 785 characters extracted
2025-06-18 14:13:53 | INFO | src.embedding.providers:_load_model:178 | Successfully loaded model: all-MiniLM-L6-v2
2025-06-18 14:13:53 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 670 characters extracted
2025-06-18 14:13:53 | INFO | src.embedding.providers:_load_model:178 | Successfully loaded model: all-MiniLM-L6-v2
2025-06-18 14:13:53 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 453 characters extracted
2025-06-18 14:13:53 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 341 characters extracted
2025-06-18 14:13:53 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 933 characters extracted
2025-06-18 14:13:53 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 3123 characters extracted
2025-06-18 14:13:53 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 428 characters extracted
2025-06-18 14:13:53 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 396 characters extracted
2025-06-18 14:13:53 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 1071 characters extracted
2025-06-18 14:13:53 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 349 characters extracted
2025-06-18 14:13:53 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 245 characters extracted
2025-06-18 14:13:53 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 287 characters extracted
2025-06-18 14:13:53 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 944 characters extracted
2025-06-18 14:13:53 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 900 characters extracted
2025-06-18 14:13:53 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 1028 characters extracted
2025-06-18 14:13:53 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 301 characters extracted
2025-06-18 14:13:53 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 713 characters extracted
2025-06-18 14:13:53 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 161 characters extracted
2025-06-18 14:13:53 | INFO | src.embedding.providers:_load_model:178 | Successfully loaded model: all-MiniLM-L6-v2
2025-06-18 14:13:53 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 753 characters extracted
2025-06-18 14:13:53 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 858 characters extracted
2025-06-18 14:13:53 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 896 characters extracted
2025-06-18 14:13:53 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 151 characters extracted
2025-06-18 14:13:53 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 825 characters extracted
2025-06-18 14:13:53 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 388 characters extracted
2025-06-18 14:13:54 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 294 characters extracted
2025-06-18 14:13:54 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 273 characters extracted
2025-06-18 14:13:54 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 665 characters extracted
2025-06-18 14:13:54 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 483 characters extracted
2025-06-18 14:13:54 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 359 characters extracted
2025-06-18 14:13:54 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 868 characters extracted
2025-06-18 14:13:54 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 551 characters extracted
2025-06-18 14:13:54 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 222 characters extracted
2025-06-18 14:13:54 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 299 characters extracted
2025-06-18 14:13:54 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 36 characters extracted
2025-06-18 14:13:54 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 553 characters extracted
2025-06-18 14:13:54 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 291 characters extracted
2025-06-18 14:13:54 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 36 characters extracted
2025-06-18 14:13:54 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 36 characters extracted
2025-06-18 14:13:54 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 304 characters extracted
2025-06-18 14:13:54 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 282 characters extracted
2025-06-18 14:13:54 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 423 characters extracted
2025-06-18 14:13:54 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 194 characters extracted
2025-06-18 14:13:54 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 349 characters extracted
2025-06-18 14:13:54 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 244 characters extracted
2025-06-18 14:13:54 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 157 characters extracted
2025-06-18 14:13:54 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 458 characters extracted
2025-06-18 14:13:54 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 332 characters extracted
2025-06-18 14:13:54 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 107 characters extracted
2025-06-18 14:13:54 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 372 characters extracted
2025-06-18 14:13:54 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 424 characters extracted
2025-06-18 14:13:54 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 382 characters extracted
2025-06-18 14:13:54 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 393 characters extracted
2025-06-18 14:13:54 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 246 characters extracted
2025-06-18 14:13:54 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 393 characters extracted
2025-06-18 14:13:54 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 280 characters extracted
2025-06-18 14:13:54 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 326 characters extracted
2025-06-18 14:13:54 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 196 characters extracted
2025-06-18 14:13:54 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 354 characters extracted
2025-06-18 14:13:54 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 290 characters extracted
2025-06-18 14:13:54 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 363 characters extracted
2025-06-18 14:13:54 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 308 characters extracted
2025-06-18 14:13:54 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 450 characters extracted
2025-06-18 14:13:54 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 370 characters extracted
2025-06-18 14:13:54 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 297 characters extracted
2025-06-18 14:13:54 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 346 characters extracted
2025-06-18 14:13:54 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 197 characters extracted
2025-06-18 14:13:54 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 395 characters extracted
2025-06-18 14:13:54 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 404 characters extracted
2025-06-18 14:13:54 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 963 characters extracted
2025-06-18 14:13:54 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 241 characters extracted
2025-06-18 14:13:55 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 393 characters extracted
2025-06-18 14:13:55 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 453 characters extracted
2025-06-18 14:13:55 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 867 characters extracted
2025-06-18 14:13:55 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 798 characters extracted
2025-06-18 14:13:55 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 699 characters extracted
2025-06-18 14:13:55 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 771 characters extracted
2025-06-18 14:13:55 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 728 characters extracted
2025-06-18 14:13:55 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 541 characters extracted
2025-06-18 14:13:55 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 355 characters extracted
2025-06-18 14:13:55 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 377 characters extracted
2025-06-18 14:13:55 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 651 characters extracted
2025-06-18 14:13:55 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 871 characters extracted
2025-06-18 14:13:55 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 604 characters extracted
2025-06-18 14:13:55 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 730 characters extracted
2025-06-18 14:13:55 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 707 characters extracted
2025-06-18 14:13:55 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 391 characters extracted
2025-06-18 14:13:55 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 397 characters extracted
2025-06-18 14:13:55 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 493 characters extracted
2025-06-18 14:13:55 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 481 characters extracted
2025-06-18 14:13:55 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 270 characters extracted
2025-06-18 14:13:55 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 821 characters extracted
2025-06-18 14:13:55 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 441 characters extracted
2025-06-18 14:13:55 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 500 characters extracted
2025-06-18 14:13:55 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 868 characters extracted
2025-06-18 14:13:55 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 793 characters extracted
2025-06-18 14:13:55 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 931 characters extracted
2025-06-18 14:13:55 | ERROR | src.database:get_connection:87 | Database connection error: ("A TVP's rows must be Sequence objects.", 'HY000')
2025-06-18 14:13:55 | ERROR | src.database:insert_batch:390 | Error inserting batch into NicheBlobETLStaging: ("A TVP's rows must be Sequence objects.", 'HY000')
2025-06-18 14:13:55 | INFO | src.pipeline:run:204 | Reached debug limit of 100 records. Processing complete.
2025-06-18 14:13:55 | INFO | src.pipeline:run:209 | Pipeline completed successfully: {'records_processed': 100, 'records_successful': 100, 'records_failed': 0, 'success_rate': 100.0, 'bytes_processed': 45337, 'text_extracted': 53083, 'duration_seconds': 5.449306, 'records_per_second': 18.350960654439298, 'error_count': 0, 'recent_errors': []}
2025-06-18 14:13:55 | INFO | __main__:main:203 | Pipeline execution completed successfully
2025-06-18 14:13:55 | INFO | __main__:main:204 | Final statistics: {
  "records_processed": 100,
  "records_successful": 100,
  "records_failed": 0,
  "success_rate": 100.0,
  "bytes_processed": 45337,
  "text_extracted": 53083,
  "duration_seconds": 5.449306,
  "records_per_second": 18.350960654439298,
  "error_count": 0,
  "recent_errors": []
}
2025-06-18 14:13:55 | INFO | __main__:run_monitoring_if_enabled:115 | Running daily monitoring tasks...
2025-06-18 14:13:55 | INFO | src.monitoring:run_daily_monitoring:461 | Starting daily monitoring tasks
2025-06-18 14:13:55 | INFO | src.monitoring:run_daily_monitoring:472 | Analyzing logs for the past 24 hours
2025-06-18 14:13:55 | INFO | src.monitoring:run_daily_monitoring:476 | Checking if alert should be sent
2025-06-18 14:13:55 | INFO | src.monitoring:send_alert:226 | Email alerts disabled - skipping alert
2025-06-18 14:13:55 | INFO | src.monitoring:run_daily_monitoring:480 | Rotating old log files
2025-06-18 14:13:55 | INFO | src.monitoring:rotate_logs:429 | Log rotation complete: 0 deleted, 1 kept, 0 bytes freed
2025-06-18 14:13:55 | INFO | src.monitoring:run_daily_monitoring:483 | Daily monitoring tasks completed successfully
2025-06-18 14:13:55 | INFO | __main__:run_monitoring_if_enabled:119 | Monitoring completed: {
  "timestamp": "2025-06-18 14:13:55.919010",
  "log_analysis": {
    "period_start": "2025-06-17 14:13:55.920010",
    "period_end": "2025-06-18 14:13:55.920010",
    "errors": [
      {
        "file": "logs\\niche_etl.log",
        "line": 38,
        "timestamp": "2025-06-18 13:55:59",
        "message": "Database connection error: ('28000', \"[28000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Login failed for user 'HRPS\\\\HRPS30783'. (18456) (SQLDriverConnect); [28000] [Microsoft][ODBC Driver 17 for SQL Server]Invalid connection string attribute (0); [28000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Login failed for user 'HRPS\\\\HRPS30783'. (18456); [28000] [Microsoft][ODBC Driver 17 for SQL Server]Invalid connection string attribute (0)\")",
        "context": "src.database:get_connection:87"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 39,
        "timestamp": "2025-06-18 13:55:59",
        "message": "Connection test failed: ('28000', \"[28000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Login failed for user 'HRPS\\\\HRPS30783'. (18456) (SQLDriverConnect); [28000] [Microsoft][ODBC Driver 17 for SQL Server]Invalid connection string attribute (0); [28000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Login failed for user 'HRPS\\\\HRPS30783'. (18456); [28000] [Microsoft][ODBC Driver 17 for SQL Server]Invalid connection string attribute (0)\")",
        "context": "src.database:test_connection:105"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 40,
        "timestamp": "2025-06-18 13:55:59",
        "message": "Configuration validation failed:",
        "context": "__main__:main:176"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 41,
        "timestamp": "2025-06-18 13:55:59",
        "message": "- Database connection error: Source database connection failed",
        "context": "__main__:main:178"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 42,
        "timestamp": "2025-06-18 13:55:59",
        "message": "Configuration Error: Configuration validation failed",
        "context": "__main__:main:225"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 74,
        "timestamp": "2025-06-18 13:57:21",
        "message": "Database connection error: ('42S02', \"[42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid object name 'NicheRMSReport.dbo.NicheBlobETLCheckpoints'. (208) (SQLExecDirectW); [42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)\")",
        "context": "src.database:get_connection:87"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 76,
        "timestamp": "2025-06-18 13:57:21",
        "message": "Database connection error: ('42S22', \"[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)\")",
        "context": "src.database:get_connection:87"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 77,
        "timestamp": "2025-06-18 13:57:21",
        "message": "Error fetching batch: ('42S22', \"[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)\")",
        "context": "src.database:fetch_batch:248"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 78,
        "timestamp": "2025-06-18 13:57:21",
        "message": "Pipeline failed: ('42S22', \"[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)\")",
        "context": "src.pipeline:run:215"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 79,
        "timestamp": "2025-06-18 13:57:21",
        "message": "Unexpected error: ('42S22', \"[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)\")",
        "context": "__main__:main:238"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 108,
        "timestamp": "2025-06-18 14:08:13",
        "message": "Database connection error: ('42000', \"[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStaging' contains more than the maximum number of prefixes. The maximum is 2. (117) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLCheckpoints' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStatistics' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLEmbeddings' contains more than the maximum number of prefixes. The maximum is 2. (117)\")",
        "context": "src.database:get_connection:87"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 109,
        "timestamp": "2025-06-18 14:08:13",
        "message": "Failed to execute SQL script: ('42000', \"[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStaging' contains more than the maximum number of prefixes. The maximum is 2. (117) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLCheckpoints' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStatistics' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLEmbeddings' contains more than the maximum number of prefixes. The maximum is 2. (117)\")",
        "context": "src.database:execute_script:407"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 110,
        "timestamp": "2025-06-18 14:08:13",
        "message": "Database setup failed: ('42000', \"[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStaging' contains more than the maximum number of prefixes. The maximum is 2. (117) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLCheckpoints' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStatistics' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLEmbeddings' contains more than the maximum number of prefixes. The maximum is 2. (117)\")",
        "context": "__main__:setup_database_if_needed:101"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 111,
        "timestamp": "2025-06-18 14:08:13",
        "message": "Pipeline Error: Database setup failed: ('42000', \"[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStaging' contains more than the maximum number of prefixes. The maximum is 2. (117) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLCheckpoints' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStatistics' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLEmbeddings' contains more than the maximum number of prefixes. The maximum is 2. (117)\")",
        "context": "__main__:main:232"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 140,
        "timestamp": "2025-06-18 14:08:36",
        "message": "Database connection error: ('42000', \"[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStaging' contains more than the maximum number of prefixes. The maximum is 2. (117) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLCheckpoints' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStatistics' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLEmbeddings' contains more than the maximum number of prefixes. The maximum is 2. (117)\")",
        "context": "src.database:get_connection:87"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 141,
        "timestamp": "2025-06-18 14:08:36",
        "message": "Failed to execute SQL script: ('42000', \"[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStaging' contains more than the maximum number of prefixes. The maximum is 2. (117) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLCheckpoints' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStatistics' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLEmbeddings' contains more than the maximum number of prefixes. The maximum is 2. (117)\")",
        "context": "src.database:execute_script:407"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 142,
        "timestamp": "2025-06-18 14:08:36",
        "message": "Database setup failed: ('42000', \"[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStaging' contains more than the maximum number of prefixes. The maximum is 2. (117) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLCheckpoints' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStatistics' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLEmbeddings' contains more than the maximum number of prefixes. The maximum is 2. (117)\")",
        "context": "__main__:setup_database_if_needed:101"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 143,
        "timestamp": "2025-06-18 14:08:36",
        "message": "Pipeline Error: Database setup failed: ('42000', \"[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStaging' contains more than the maximum number of prefixes. The maximum is 2. (117) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLCheckpoints' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStatistics' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLEmbeddings' contains more than the maximum number of prefixes. The maximum is 2. (117)\")",
        "context": "__main__:main:232"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 178,
        "timestamp": "2025-06-18 14:10:42",
        "message": "Database connection error: ('42S02', \"[42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid object name 'NicheRMSReport.dbo.NicheBlobETLCheckpoints'. (208) (SQLExecDirectW); [42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)\")",
        "context": "src.database:get_connection:87"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 191,
        "timestamp": "2025-06-18 14:10:46",
        "message": "Embedding processing failed for record 10023001000000046128477: Single text embedding failed: Failed to generate embedding: Failed to load model all-MiniLM-L6-v2: Cannot copy out of meta tensor; no data! Please use torch.nn.Module.to_empty() instead of torch.nn.Module.to() when moving module from meta to a different device.",
        "context": "src.embedding.processor:process_text:93"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 293,
        "timestamp": "2025-06-18 14:10:49",
        "message": "Database connection error: ('22003', '[22003] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Arithmetic overflow error converting expression to data type bigint. (8115) (SQLExecDirectW); [22003] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The statement has been terminated. (3621)')",
        "context": "src.database:get_connection:87"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 294,
        "timestamp": "2025-06-18 14:10:49",
        "message": "Error inserting batch into NicheBlobETLStaging: ('22003', '[22003] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Arithmetic overflow error converting expression to data type bigint. (8115) (SQLExecDirectW); [22003] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The statement has been terminated. (3621)')",
        "context": "src.database:insert_batch:390"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 722,
        "timestamp": "2025-06-18 14:13:50",
        "message": "Database connection error: ('42S02', \"[42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid object name 'NicheRMSReport.dbo.NicheBlobETLCheckpoints'. (208) (SQLExecDirectW); [42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)\")",
        "context": "src.database:get_connection:87"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 835,
        "timestamp": "2025-06-18 14:13:55",
        "message": "Database connection error: (\"A TVP's rows must be Sequence objects.\", 'HY000')",
        "context": "src.database:get_connection:87"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 836,
        "timestamp": "2025-06-18 14:13:55",
        "message": "Error inserting batch into NicheBlobETLStaging: (\"A TVP's rows must be Sequence objects.\", 'HY000')",
        "context": "src.database:insert_batch:390"
      }
    ],
    "warnings": [
      {
        "file": "logs\\niche_etl.log",
        "line": 19,
        "timestamp": "2025-06-18 13:55:47",
        "message": "Configuration warning: Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "src.config_validator:add_warning:22"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 30,
        "timestamp": "2025-06-18 13:55:59",
        "message": "Configuration warnings found:",
        "context": "src.config_validator:_log_validation_summary:252"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 31,
        "timestamp": "2025-06-18 13:55:59",
        "message": "- Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "src.config_validator:_log_validation_summary:254"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 47,
        "timestamp": "2025-06-18 13:57:09",
        "message": "Configuration warning: Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "src.config_validator:add_warning:22"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 58,
        "timestamp": "2025-06-18 13:57:20",
        "message": "Configuration warnings found:",
        "context": "src.config_validator:_log_validation_summary:252"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 59,
        "timestamp": "2025-06-18 13:57:20",
        "message": "- Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "src.config_validator:_log_validation_summary:254"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 67,
        "timestamp": "2025-06-18 13:57:21",
        "message": "Configuration warnings:",
        "context": "__main__:main:186"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 68,
        "timestamp": "2025-06-18 13:57:21",
        "message": "- Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "__main__:main:188"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 75,
        "timestamp": "2025-06-18 13:57:21",
        "message": "Could not retrieve last processed ID for niche_text_etl: ('42S02', \"[42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid object name 'NicheRMSReport.dbo.NicheBlobETLCheckpoints'. (208) (SQLExecDirectW); [42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)\"). Starting from 0.",
        "context": "src.database:get_last_processed_id:126"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 84,
        "timestamp": "2025-06-18 14:07:59",
        "message": "Configuration warning: Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "src.config_validator:add_warning:22"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 95,
        "timestamp": "2025-06-18 14:08:13",
        "message": "Configuration warnings found:",
        "context": "src.config_validator:_log_validation_summary:252"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 96,
        "timestamp": "2025-06-18 14:08:13",
        "message": "- Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "src.config_validator:_log_validation_summary:254"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 104,
        "timestamp": "2025-06-18 14:08:13",
        "message": "Configuration warnings:",
        "context": "__main__:main:186"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 105,
        "timestamp": "2025-06-18 14:08:13",
        "message": "- Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "__main__:main:188"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 116,
        "timestamp": "2025-06-18 14:08:26",
        "message": "Configuration warning: Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "src.config_validator:add_warning:22"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 127,
        "timestamp": "2025-06-18 14:08:36",
        "message": "Configuration warnings found:",
        "context": "src.config_validator:_log_validation_summary:252"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 128,
        "timestamp": "2025-06-18 14:08:36",
        "message": "- Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "src.config_validator:_log_validation_summary:254"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 136,
        "timestamp": "2025-06-18 14:08:36",
        "message": "Configuration warnings:",
        "context": "__main__:main:186"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 137,
        "timestamp": "2025-06-18 14:08:36",
        "message": "- Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "__main__:main:188"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 148,
        "timestamp": "2025-06-18 14:10:31",
        "message": "Configuration warning: Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "src.config_validator:add_warning:22"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 159,
        "timestamp": "2025-06-18 14:10:42",
        "message": "Configuration warnings found:",
        "context": "src.config_validator:_log_validation_summary:252"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 160,
        "timestamp": "2025-06-18 14:10:42",
        "message": "- Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "src.config_validator:_log_validation_summary:254"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 168,
        "timestamp": "2025-06-18 14:10:42",
        "message": "Configuration warnings:",
        "context": "__main__:main:186"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 169,
        "timestamp": "2025-06-18 14:10:42",
        "message": "- Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "__main__:main:188"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 179,
        "timestamp": "2025-06-18 14:10:42",
        "message": "Could not retrieve last processed ID for niche_text_etl: ('42S02', \"[42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid object name 'NicheRMSReport.dbo.NicheBlobETLCheckpoints'. (208) (SQLExecDirectW); [42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)\"). Starting from 0.",
        "context": "src.database:get_last_processed_id:126"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 692,
        "timestamp": "2025-06-18 14:13:39",
        "message": "Configuration warning: Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "src.config_validator:add_warning:22"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 703,
        "timestamp": "2025-06-18 14:13:50",
        "message": "Configuration warnings found:",
        "context": "src.config_validator:_log_validation_summary:252"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 704,
        "timestamp": "2025-06-18 14:13:50",
        "message": "- Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "src.config_validator:_log_validation_summary:254"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 712,
        "timestamp": "2025-06-18 14:13:50",
        "message": "Configuration warnings:",
        "context": "__main__:main:186"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 713,
        "timestamp": "2025-06-18 14:13:50",
        "message": "- Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "__main__:main:188"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 723,
        "timestamp": "2025-06-18 14:13:50",
        "message": "Could not retrieve last processed ID for niche_text_etl: ('42S02', \"[42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid object name 'NicheRMSReport.dbo.NicheBlobETLCheckpoints'. (208) (SQLExecDirectW); [42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)\"). Starting from 0.",
        "context": "src.database:get_last_processed_id:126"
      }
    ],
    "total_errors": 25,
    "total_warnings": 31,
    "files_analyzed": [
      "logs\\niche_etl.log"
    ],
    "summary": {
      "has_errors": true,
      "has_warnings": true,
      "error_categories": {
        "Database": 15,
        "Other": 10
      },
      "warning_categories": {
        "Database": 17,
        "Other": 14
      },
      "most_common_errors": [],
      "most_common_warnings": []
    }
  },
  "email_sent": true,
  "log_rotation": {
    "files_deleted": [],
    "files_kept": [
      "logs\\niche_etl.log"
    ],
    "total_deleted": 0,
    "total_kept": 1,
    "space_freed": 0
  }
}
2025-06-18 14:13:55 | INFO | __main__:main:215 | Niche Text ETL completed successfully
2025-06-18 14:26:05 | INFO | src.logging_setup:setup_logging:46 | Logging initialized - Level: INFO, File: logs\niche_etl.log
2025-06-18 14:26:05 | INFO | __main__:main:160 | Niche Text ETL starting with simplified execution model
2025-06-18 14:26:05 | INFO | __main__:main:163 | Initializing pipeline...
2025-06-18 14:26:05 | INFO | src.config_validator:validate_all:55 | Starting comprehensive configuration validation...
2025-06-18 14:26:05 | WARNING | src.config_validator:add_warning:22 | Configuration warning: Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed
2025-06-18 14:26:05 | INFO | src.config_validator:set_module_flag:34 | Module 'email_enabled' is disabled
2025-06-18 14:26:15 | INFO | src.config_validator:_validate_embedding_module:169 | Text embedding model accessibility test successful
2025-06-18 14:26:15 | INFO | src.config_validator:set_module_flag:34 | Module 'embedding_enabled' is enabled
2025-06-18 14:26:15 | INFO | src.config_validator:_log_validation_summary:240 | Configuration validation completed
2025-06-18 14:26:15 | INFO | src.config_validator:_log_validation_summary:241 | Overall validation status: PASSED
2025-06-18 14:26:15 | INFO | src.config_validator:_log_validation_summary:242 | Warnings: 1
2025-06-18 14:26:15 | INFO | src.config_validator:_log_validation_summary:243 | Errors: 0
2025-06-18 14:26:15 | INFO | src.config_validator:_log_validation_summary:246 | Module status:
2025-06-18 14:26:15 | INFO | src.config_validator:_log_validation_summary:249 |   - email_enabled: DISABLED
2025-06-18 14:26:15 | INFO | src.config_validator:_log_validation_summary:249 |   - embedding_enabled: ENABLED
2025-06-18 14:26:15 | WARNING | src.config_validator:_log_validation_summary:252 | Configuration warnings found:
2025-06-18 14:26:15 | WARNING | src.config_validator:_log_validation_summary:254 |   - Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed
2025-06-18 14:26:15 | INFO | src.embedding.factory:create_provider:46 | Created embedding provider: sentence_transformers
2025-06-18 14:26:15 | INFO | src.embedding.processor:_initialize_provider:45 | Initialized embedding provider: sentence_transformers
2025-06-18 14:26:15 | INFO | src.pipeline:__init__:120 | Text embedding processor initialized
2025-06-18 14:26:15 | INFO | src.pipeline:__init__:125 | Pipeline initialized successfully
2025-06-18 14:26:15 | INFO | __main__:main:169 | Validating configuration...
2025-06-18 14:26:15 | INFO | src.pipeline:_test_connections:220 | Testing database connections...
2025-06-18 14:26:15 | INFO | src.pipeline:_test_connections:228 | Database connections successful
2025-06-18 14:26:15 | WARNING | __main__:main:186 | Configuration warnings:
2025-06-18 14:26:15 | WARNING | __main__:main:188 |   - Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed
2025-06-18 14:26:15 | INFO | __main__:main:190 | Configuration validation completed
2025-06-18 14:26:15 | INFO | __main__:setup_database_if_needed:70 | Auto-setting up database...
2025-06-18 14:26:15 | INFO | src.database:execute_script:405 | SQL script executed successfully.
2025-06-18 14:26:15 | INFO | __main__:setup_database_if_needed:98 | Database setup completed successfully
2025-06-18 14:26:15 | INFO | __main__:main:199 | Starting ETL pipeline execution...
2025-06-18 14:26:15 | INFO | src.pipeline:run:133 | Starting Niche Text ETL Pipeline
2025-06-18 14:26:15 | INFO | src.pipeline:_test_connections:220 | Testing database connections...
2025-06-18 14:26:15 | INFO | src.pipeline:_test_connections:228 | Database connections successful
2025-06-18 14:26:15 | ERROR | src.database:get_connection:87 | Database connection error: ('42S02', "[42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid object name 'NicheRMSReport.dbo.NicheBlobETLCheckpoints'. (208) (SQLExecDirectW); [42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)")
2025-06-18 14:26:15 | WARNING | src.database:get_last_processed_id:126 | Could not retrieve last processed ID for niche_text_etl: ('42S02', "[42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid object name 'NicheRMSReport.dbo.NicheBlobETLCheckpoints'. (208) (SQLExecDirectW); [42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)"). Starting from 0.
2025-06-18 14:26:17 | INFO | src.database:fetch_batch:340 | Fetched batch of 1000 records (IDs: 10023001000000046127415 - 10023001000000046336135)
2025-06-18 14:26:17 | INFO | src.pipeline:run:166 | Trimming batch to 5 records to stay within debug limit
2025-06-18 14:26:17 | INFO | src.pipeline:run:172 | Processing batch 1 with 5 records (total: 5/5)
2025-06-18 14:26:17 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 1098 characters extracted
2025-06-18 14:26:17 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 666 characters extracted
2025-06-18 14:26:17 | INFO | src.embedding.providers:_load_model:175 | Loading Sentence Transformers model: all-MiniLM-L6-v2
2025-06-18 14:26:17 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 991 characters extracted
2025-06-18 14:26:17 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 1592 characters extracted
2025-06-18 14:26:17 | INFO | src.embedding.providers:_load_model:175 | Loading Sentence Transformers model: all-MiniLM-L6-v2
2025-06-18 14:26:17 | INFO | src.embedding.providers:_load_model:175 | Loading Sentence Transformers model: all-MiniLM-L6-v2
2025-06-18 14:26:17 | INFO | src.embedding.providers:_load_model:175 | Loading Sentence Transformers model: all-MiniLM-L6-v2
2025-06-18 14:26:18 | WARNING | src.embedding.processor:_process_chunked_text:160 | Failed to embed chunk 0 for record 10023001000000046127415: Failed to generate embedding: Failed to load model all-MiniLM-L6-v2: Cannot copy out of meta tensor; no data! Please use torch.nn.Module.to_empty() instead of torch.nn.Module.to() when moving module from meta to a different device.
2025-06-18 14:26:18 | INFO | src.embedding.providers:_load_model:175 | Loading Sentence Transformers model: all-MiniLM-L6-v2
2025-06-18 14:26:18 | ERROR | src.embedding.processor:process_text:93 | Embedding processing failed for record 10023001000000046128477: Single text embedding failed: Failed to generate embedding: Failed to load model all-MiniLM-L6-v2: Cannot copy out of meta tensor; no data! Please use torch.nn.Module.to_empty() instead of torch.nn.Module.to() when moving module from meta to a different device.
2025-06-18 14:26:18 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 785 characters extracted
2025-06-18 14:26:18 | INFO | src.embedding.providers:_load_model:175 | Loading Sentence Transformers model: all-MiniLM-L6-v2
2025-06-18 14:26:18 | WARNING | src.embedding.processor:_process_chunked_text:160 | Failed to embed chunk 0 for record 10023001000000046128594: Failed to generate embedding: Failed to load model all-MiniLM-L6-v2: Cannot copy out of meta tensor; no data! Please use torch.nn.Module.to_empty() instead of torch.nn.Module.to() when moving module from meta to a different device.
2025-06-18 14:26:18 | INFO | src.embedding.providers:_load_model:175 | Loading Sentence Transformers model: all-MiniLM-L6-v2
2025-06-18 14:26:18 | ERROR | src.embedding.processor:process_text:93 | Embedding processing failed for record 10023001000000046127433: Single text embedding failed: Failed to generate embedding: Failed to load model all-MiniLM-L6-v2: Cannot copy out of meta tensor; no data! Please use torch.nn.Module.to_empty() instead of torch.nn.Module.to() when moving module from meta to a different device.
2025-06-18 14:26:18 | WARNING | src.embedding.processor:_process_chunked_text:160 | Failed to embed chunk 1 for record 10023001000000046127415: Failed to generate embedding: Failed to load model all-MiniLM-L6-v2: Cannot copy out of meta tensor; no data! Please use torch.nn.Module.to_empty() instead of torch.nn.Module.to() when moving module from meta to a different device.
2025-06-18 14:26:18 | ERROR | src.embedding.processor:process_text:93 | Embedding processing failed for record 10023001000000046127415: Chunked text embedding failed: No chunks could be embedded
2025-06-18 14:26:19 | WARNING | src.embedding.processor:_process_chunked_text:160 | Failed to embed chunk 1 for record 10023001000000046128594: Failed to generate embedding: Failed to load model all-MiniLM-L6-v2: Cannot copy out of meta tensor; no data! Please use torch.nn.Module.to_empty() instead of torch.nn.Module.to() when moving module from meta to a different device.
2025-06-18 14:26:19 | ERROR | src.embedding.processor:process_text:93 | Embedding processing failed for record 10023001000000046128594: Chunked text embedding failed: No chunks could be embedded
2025-06-18 14:26:19 | ERROR | src.embedding.processor:process_text:93 | Embedding processing failed for record 10023001000000046128942: Single text embedding failed: Failed to generate embedding: Failed to load model all-MiniLM-L6-v2: Cannot copy out of meta tensor; no data! Please use torch.nn.Module.to_empty() instead of torch.nn.Module.to() when moving module from meta to a different device.
2025-06-18 14:26:19 | ERROR | src.database:get_connection:87 | Database connection error: ('22003', '[22003] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Arithmetic overflow error converting expression to data type bigint. (8115) (SQLExecDirectW); [22003] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The statement has been terminated. (3621)')
2025-06-18 14:26:19 | ERROR | src.database:insert_batch:390 | Error inserting batch into NicheBlobETLStaging: ('22003', '[22003] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Arithmetic overflow error converting expression to data type bigint. (8115) (SQLExecDirectW); [22003] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The statement has been terminated. (3621)')
2025-06-18 14:26:19 | INFO | src.pipeline:run:204 | Reached debug limit of 5 records. Processing complete.
2025-06-18 14:26:19 | INFO | src.pipeline:run:209 | Pipeline completed successfully: {'records_processed': 5, 'records_successful': 5, 'records_failed': 0, 'success_rate': 100.0, 'bytes_processed': 3378, 'text_extracted': 5132, 'duration_seconds': 3.612284, 'records_per_second': 1.3841658075610888, 'error_count': 0, 'recent_errors': []}
2025-06-18 14:26:19 | INFO | __main__:main:203 | Pipeline execution completed successfully
2025-06-18 14:26:19 | INFO | __main__:main:204 | Final statistics: {
  "records_processed": 5,
  "records_successful": 5,
  "records_failed": 0,
  "success_rate": 100.0,
  "bytes_processed": 3378,
  "text_extracted": 5132,
  "duration_seconds": 3.612284,
  "records_per_second": 1.3841658075610888,
  "error_count": 0,
  "recent_errors": []
}
2025-06-18 14:26:19 | INFO | __main__:run_monitoring_if_enabled:115 | Running daily monitoring tasks...
2025-06-18 14:26:19 | INFO | src.monitoring:run_daily_monitoring:461 | Starting daily monitoring tasks
2025-06-18 14:26:19 | INFO | src.monitoring:run_daily_monitoring:472 | Analyzing logs for the past 24 hours
2025-06-18 14:26:19 | INFO | src.monitoring:run_daily_monitoring:476 | Checking if alert should be sent
2025-06-18 14:26:19 | INFO | src.monitoring:send_alert:226 | Email alerts disabled - skipping alert
2025-06-18 14:26:19 | INFO | src.monitoring:run_daily_monitoring:480 | Rotating old log files
2025-06-18 14:26:19 | INFO | src.monitoring:rotate_logs:429 | Log rotation complete: 0 deleted, 1 kept, 0 bytes freed
2025-06-18 14:26:19 | INFO | src.monitoring:run_daily_monitoring:483 | Daily monitoring tasks completed successfully
2025-06-18 14:26:19 | INFO | __main__:run_monitoring_if_enabled:119 | Monitoring completed: {
  "timestamp": "2025-06-18 14:26:19.043169",
  "log_analysis": {
    "period_start": "2025-06-17 14:26:19.043169",
    "period_end": "2025-06-18 14:26:19.043169",
    "errors": [
      {
        "file": "logs\\niche_etl.log",
        "line": 38,
        "timestamp": "2025-06-18 13:55:59",
        "message": "Database connection error: ('28000', \"[28000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Login failed for user 'HRPS\\\\HRPS30783'. (18456) (SQLDriverConnect); [28000] [Microsoft][ODBC Driver 17 for SQL Server]Invalid connection string attribute (0); [28000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Login failed for user 'HRPS\\\\HRPS30783'. (18456); [28000] [Microsoft][ODBC Driver 17 for SQL Server]Invalid connection string attribute (0)\")",
        "context": "src.database:get_connection:87"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 39,
        "timestamp": "2025-06-18 13:55:59",
        "message": "Connection test failed: ('28000', \"[28000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Login failed for user 'HRPS\\\\HRPS30783'. (18456) (SQLDriverConnect); [28000] [Microsoft][ODBC Driver 17 for SQL Server]Invalid connection string attribute (0); [28000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Login failed for user 'HRPS\\\\HRPS30783'. (18456); [28000] [Microsoft][ODBC Driver 17 for SQL Server]Invalid connection string attribute (0)\")",
        "context": "src.database:test_connection:105"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 40,
        "timestamp": "2025-06-18 13:55:59",
        "message": "Configuration validation failed:",
        "context": "__main__:main:176"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 41,
        "timestamp": "2025-06-18 13:55:59",
        "message": "- Database connection error: Source database connection failed",
        "context": "__main__:main:178"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 42,
        "timestamp": "2025-06-18 13:55:59",
        "message": "Configuration Error: Configuration validation failed",
        "context": "__main__:main:225"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 74,
        "timestamp": "2025-06-18 13:57:21",
        "message": "Database connection error: ('42S02', \"[42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid object name 'NicheRMSReport.dbo.NicheBlobETLCheckpoints'. (208) (SQLExecDirectW); [42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)\")",
        "context": "src.database:get_connection:87"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 76,
        "timestamp": "2025-06-18 13:57:21",
        "message": "Database connection error: ('42S22', \"[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)\")",
        "context": "src.database:get_connection:87"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 77,
        "timestamp": "2025-06-18 13:57:21",
        "message": "Error fetching batch: ('42S22', \"[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)\")",
        "context": "src.database:fetch_batch:248"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 78,
        "timestamp": "2025-06-18 13:57:21",
        "message": "Pipeline failed: ('42S22', \"[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)\")",
        "context": "src.pipeline:run:215"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 79,
        "timestamp": "2025-06-18 13:57:21",
        "message": "Unexpected error: ('42S22', \"[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'real_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'fixed_type'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)\")",
        "context": "__main__:main:238"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 108,
        "timestamp": "2025-06-18 14:08:13",
        "message": "Database connection error: ('42000', \"[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStaging' contains more than the maximum number of prefixes. The maximum is 2. (117) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLCheckpoints' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStatistics' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLEmbeddings' contains more than the maximum number of prefixes. The maximum is 2. (117)\")",
        "context": "src.database:get_connection:87"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 109,
        "timestamp": "2025-06-18 14:08:13",
        "message": "Failed to execute SQL script: ('42000', \"[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStaging' contains more than the maximum number of prefixes. The maximum is 2. (117) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLCheckpoints' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStatistics' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLEmbeddings' contains more than the maximum number of prefixes. The maximum is 2. (117)\")",
        "context": "src.database:execute_script:407"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 110,
        "timestamp": "2025-06-18 14:08:13",
        "message": "Database setup failed: ('42000', \"[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStaging' contains more than the maximum number of prefixes. The maximum is 2. (117) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLCheckpoints' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStatistics' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLEmbeddings' contains more than the maximum number of prefixes. The maximum is 2. (117)\")",
        "context": "__main__:setup_database_if_needed:101"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 111,
        "timestamp": "2025-06-18 14:08:13",
        "message": "Pipeline Error: Database setup failed: ('42000', \"[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStaging' contains more than the maximum number of prefixes. The maximum is 2. (117) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLCheckpoints' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStatistics' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLEmbeddings' contains more than the maximum number of prefixes. The maximum is 2. (117)\")",
        "context": "__main__:main:232"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 140,
        "timestamp": "2025-06-18 14:08:36",
        "message": "Database connection error: ('42000', \"[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStaging' contains more than the maximum number of prefixes. The maximum is 2. (117) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLCheckpoints' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStatistics' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLEmbeddings' contains more than the maximum number of prefixes. The maximum is 2. (117)\")",
        "context": "src.database:get_connection:87"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 141,
        "timestamp": "2025-06-18 14:08:36",
        "message": "Failed to execute SQL script: ('42000', \"[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStaging' contains more than the maximum number of prefixes. The maximum is 2. (117) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLCheckpoints' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStatistics' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLEmbeddings' contains more than the maximum number of prefixes. The maximum is 2. (117)\")",
        "context": "src.database:execute_script:407"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 142,
        "timestamp": "2025-06-18 14:08:36",
        "message": "Database setup failed: ('42000', \"[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStaging' contains more than the maximum number of prefixes. The maximum is 2. (117) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLCheckpoints' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStatistics' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLEmbeddings' contains more than the maximum number of prefixes. The maximum is 2. (117)\")",
        "context": "__main__:setup_database_if_needed:101"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 143,
        "timestamp": "2025-06-18 14:08:36",
        "message": "Pipeline Error: Database setup failed: ('42000', \"[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStaging' contains more than the maximum number of prefixes. The maximum is 2. (117) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLCheckpoints' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLStatistics' contains more than the maximum number of prefixes. The maximum is 2. (117); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The object name 'HQPRCAD94BI03.PA_PROD.dbo.NicheBlobETLEmbeddings' contains more than the maximum number of prefixes. The maximum is 2. (117)\")",
        "context": "__main__:main:232"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 178,
        "timestamp": "2025-06-18 14:10:42",
        "message": "Database connection error: ('42S02', \"[42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid object name 'NicheRMSReport.dbo.NicheBlobETLCheckpoints'. (208) (SQLExecDirectW); [42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)\")",
        "context": "src.database:get_connection:87"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 191,
        "timestamp": "2025-06-18 14:10:46",
        "message": "Embedding processing failed for record 10023001000000046128477: Single text embedding failed: Failed to generate embedding: Failed to load model all-MiniLM-L6-v2: Cannot copy out of meta tensor; no data! Please use torch.nn.Module.to_empty() instead of torch.nn.Module.to() when moving module from meta to a different device.",
        "context": "src.embedding.processor:process_text:93"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 293,
        "timestamp": "2025-06-18 14:10:49",
        "message": "Database connection error: ('22003', '[22003] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Arithmetic overflow error converting expression to data type bigint. (8115) (SQLExecDirectW); [22003] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The statement has been terminated. (3621)')",
        "context": "src.database:get_connection:87"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 294,
        "timestamp": "2025-06-18 14:10:49",
        "message": "Error inserting batch into NicheBlobETLStaging: ('22003', '[22003] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Arithmetic overflow error converting expression to data type bigint. (8115) (SQLExecDirectW); [22003] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The statement has been terminated. (3621)')",
        "context": "src.database:insert_batch:390"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 722,
        "timestamp": "2025-06-18 14:13:50",
        "message": "Database connection error: ('42S02', \"[42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid object name 'NicheRMSReport.dbo.NicheBlobETLCheckpoints'. (208) (SQLExecDirectW); [42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)\")",
        "context": "src.database:get_connection:87"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 835,
        "timestamp": "2025-06-18 14:13:55",
        "message": "Database connection error: (\"A TVP's rows must be Sequence objects.\", 'HY000')",
        "context": "src.database:get_connection:87"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 836,
        "timestamp": "2025-06-18 14:13:55",
        "message": "Error inserting batch into NicheBlobETLStaging: (\"A TVP's rows must be Sequence objects.\", 'HY000')",
        "context": "src.database:insert_batch:390"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 1327,
        "timestamp": "2025-06-18 14:26:15",
        "message": "Database connection error: ('42S02', \"[42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid object name 'NicheRMSReport.dbo.NicheBlobETLCheckpoints'. (208) (SQLExecDirectW); [42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)\")",
        "context": "src.database:get_connection:87"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 1342,
        "timestamp": "2025-06-18 14:26:18",
        "message": "Embedding processing failed for record 10023001000000046128477: Single text embedding failed: Failed to generate embedding: Failed to load model all-MiniLM-L6-v2: Cannot copy out of meta tensor; no data! Please use torch.nn.Module.to_empty() instead of torch.nn.Module.to() when moving module from meta to a different device.",
        "context": "src.embedding.processor:process_text:93"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 1347,
        "timestamp": "2025-06-18 14:26:18",
        "message": "Embedding processing failed for record 10023001000000046127433: Single text embedding failed: Failed to generate embedding: Failed to load model all-MiniLM-L6-v2: Cannot copy out of meta tensor; no data! Please use torch.nn.Module.to_empty() instead of torch.nn.Module.to() when moving module from meta to a different device.",
        "context": "src.embedding.processor:process_text:93"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 1349,
        "timestamp": "2025-06-18 14:26:18",
        "message": "Embedding processing failed for record 10023001000000046127415: Chunked text embedding failed: No chunks could be embedded",
        "context": "src.embedding.processor:process_text:93"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 1351,
        "timestamp": "2025-06-18 14:26:19",
        "message": "Embedding processing failed for record 10023001000000046128594: Chunked text embedding failed: No chunks could be embedded",
        "context": "src.embedding.processor:process_text:93"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 1352,
        "timestamp": "2025-06-18 14:26:19",
        "message": "Embedding processing failed for record 10023001000000046128942: Single text embedding failed: Failed to generate embedding: Failed to load model all-MiniLM-L6-v2: Cannot copy out of meta tensor; no data! Please use torch.nn.Module.to_empty() instead of torch.nn.Module.to() when moving module from meta to a different device.",
        "context": "src.embedding.processor:process_text:93"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 1353,
        "timestamp": "2025-06-18 14:26:19",
        "message": "Database connection error: ('22003', '[22003] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Arithmetic overflow error converting expression to data type bigint. (8115) (SQLExecDirectW); [22003] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The statement has been terminated. (3621)')",
        "context": "src.database:get_connection:87"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 1354,
        "timestamp": "2025-06-18 14:26:19",
        "message": "Error inserting batch into NicheBlobETLStaging: ('22003', '[22003] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Arithmetic overflow error converting expression to data type bigint. (8115) (SQLExecDirectW); [22003] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The statement has been terminated. (3621)')",
        "context": "src.database:insert_batch:390"
      }
    ],
    "warnings": [
      {
        "file": "logs\\niche_etl.log",
        "line": 19,
        "timestamp": "2025-06-18 13:55:47",
        "message": "Configuration warning: Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "src.config_validator:add_warning:22"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 30,
        "timestamp": "2025-06-18 13:55:59",
        "message": "Configuration warnings found:",
        "context": "src.config_validator:_log_validation_summary:252"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 31,
        "timestamp": "2025-06-18 13:55:59",
        "message": "- Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "src.config_validator:_log_validation_summary:254"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 47,
        "timestamp": "2025-06-18 13:57:09",
        "message": "Configuration warning: Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "src.config_validator:add_warning:22"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 58,
        "timestamp": "2025-06-18 13:57:20",
        "message": "Configuration warnings found:",
        "context": "src.config_validator:_log_validation_summary:252"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 59,
        "timestamp": "2025-06-18 13:57:20",
        "message": "- Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "src.config_validator:_log_validation_summary:254"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 67,
        "timestamp": "2025-06-18 13:57:21",
        "message": "Configuration warnings:",
        "context": "__main__:main:186"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 68,
        "timestamp": "2025-06-18 13:57:21",
        "message": "- Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "__main__:main:188"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 75,
        "timestamp": "2025-06-18 13:57:21",
        "message": "Could not retrieve last processed ID for niche_text_etl: ('42S02', \"[42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid object name 'NicheRMSReport.dbo.NicheBlobETLCheckpoints'. (208) (SQLExecDirectW); [42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)\"). Starting from 0.",
        "context": "src.database:get_last_processed_id:126"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 84,
        "timestamp": "2025-06-18 14:07:59",
        "message": "Configuration warning: Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "src.config_validator:add_warning:22"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 95,
        "timestamp": "2025-06-18 14:08:13",
        "message": "Configuration warnings found:",
        "context": "src.config_validator:_log_validation_summary:252"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 96,
        "timestamp": "2025-06-18 14:08:13",
        "message": "- Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "src.config_validator:_log_validation_summary:254"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 104,
        "timestamp": "2025-06-18 14:08:13",
        "message": "Configuration warnings:",
        "context": "__main__:main:186"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 105,
        "timestamp": "2025-06-18 14:08:13",
        "message": "- Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "__main__:main:188"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 116,
        "timestamp": "2025-06-18 14:08:26",
        "message": "Configuration warning: Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "src.config_validator:add_warning:22"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 127,
        "timestamp": "2025-06-18 14:08:36",
        "message": "Configuration warnings found:",
        "context": "src.config_validator:_log_validation_summary:252"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 128,
        "timestamp": "2025-06-18 14:08:36",
        "message": "- Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "src.config_validator:_log_validation_summary:254"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 136,
        "timestamp": "2025-06-18 14:08:36",
        "message": "Configuration warnings:",
        "context": "__main__:main:186"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 137,
        "timestamp": "2025-06-18 14:08:36",
        "message": "- Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "__main__:main:188"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 148,
        "timestamp": "2025-06-18 14:10:31",
        "message": "Configuration warning: Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "src.config_validator:add_warning:22"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 159,
        "timestamp": "2025-06-18 14:10:42",
        "message": "Configuration warnings found:",
        "context": "src.config_validator:_log_validation_summary:252"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 160,
        "timestamp": "2025-06-18 14:10:42",
        "message": "- Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "src.config_validator:_log_validation_summary:254"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 168,
        "timestamp": "2025-06-18 14:10:42",
        "message": "Configuration warnings:",
        "context": "__main__:main:186"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 169,
        "timestamp": "2025-06-18 14:10:42",
        "message": "- Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "__main__:main:188"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 179,
        "timestamp": "2025-06-18 14:10:42",
        "message": "Could not retrieve last processed ID for niche_text_etl: ('42S02', \"[42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid object name 'NicheRMSReport.dbo.NicheBlobETLCheckpoints'. (208) (SQLExecDirectW); [42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)\"). Starting from 0.",
        "context": "src.database:get_last_processed_id:126"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 692,
        "timestamp": "2025-06-18 14:13:39",
        "message": "Configuration warning: Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "src.config_validator:add_warning:22"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 703,
        "timestamp": "2025-06-18 14:13:50",
        "message": "Configuration warnings found:",
        "context": "src.config_validator:_log_validation_summary:252"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 704,
        "timestamp": "2025-06-18 14:13:50",
        "message": "- Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "src.config_validator:_log_validation_summary:254"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 712,
        "timestamp": "2025-06-18 14:13:50",
        "message": "Configuration warnings:",
        "context": "__main__:main:186"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 713,
        "timestamp": "2025-06-18 14:13:50",
        "message": "- Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "__main__:main:188"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 723,
        "timestamp": "2025-06-18 14:13:50",
        "message": "Could not retrieve last processed ID for niche_text_etl: ('42S02', \"[42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid object name 'NicheRMSReport.dbo.NicheBlobETLCheckpoints'. (208) (SQLExecDirectW); [42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)\"). Starting from 0.",
        "context": "src.database:get_last_processed_id:126"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 1297,
        "timestamp": "2025-06-18 14:26:05",
        "message": "Configuration warning: Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "src.config_validator:add_warning:22"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 1308,
        "timestamp": "2025-06-18 14:26:15",
        "message": "Configuration warnings found:",
        "context": "src.config_validator:_log_validation_summary:252"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 1309,
        "timestamp": "2025-06-18 14:26:15",
        "message": "- Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "src.config_validator:_log_validation_summary:254"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 1317,
        "timestamp": "2025-06-18 14:26:15",
        "message": "Configuration warnings:",
        "context": "__main__:main:186"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 1318,
        "timestamp": "2025-06-18 14:26:15",
        "message": "- Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "__main__:main:188"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 1328,
        "timestamp": "2025-06-18 14:26:15",
        "message": "Could not retrieve last processed ID for niche_text_etl: ('42S02', \"[42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid object name 'NicheRMSReport.dbo.NicheBlobETLCheckpoints'. (208) (SQLExecDirectW); [42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)\"). Starting from 0.",
        "context": "src.database:get_last_processed_id:126"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 1340,
        "timestamp": "2025-06-18 14:26:18",
        "message": "Failed to embed chunk 0 for record 10023001000000046127415: Failed to generate embedding: Failed to load model all-MiniLM-L6-v2: Cannot copy out of meta tensor; no data! Please use torch.nn.Module.to_empty() instead of torch.nn.Module.to() when moving module from meta to a different device.",
        "context": "src.embedding.processor:_process_chunked_text:160"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 1345,
        "timestamp": "2025-06-18 14:26:18",
        "message": "Failed to embed chunk 0 for record 10023001000000046128594: Failed to generate embedding: Failed to load model all-MiniLM-L6-v2: Cannot copy out of meta tensor; no data! Please use torch.nn.Module.to_empty() instead of torch.nn.Module.to() when moving module from meta to a different device.",
        "context": "src.embedding.processor:_process_chunked_text:160"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 1348,
        "timestamp": "2025-06-18 14:26:18",
        "message": "Failed to embed chunk 1 for record 10023001000000046127415: Failed to generate embedding: Failed to load model all-MiniLM-L6-v2: Cannot copy out of meta tensor; no data! Please use torch.nn.Module.to_empty() instead of torch.nn.Module.to() when moving module from meta to a different device.",
        "context": "src.embedding.processor:_process_chunked_text:160"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 1350,
        "timestamp": "2025-06-18 14:26:19",
        "message": "Failed to embed chunk 1 for record 10023001000000046128594: Failed to generate embedding: Failed to load model all-MiniLM-L6-v2: Cannot copy out of meta tensor; no data! Please use torch.nn.Module.to_empty() instead of torch.nn.Module.to() when moving module from meta to a different device.",
        "context": "src.embedding.processor:_process_chunked_text:160"
      }
    ],
    "total_errors": 33,
    "total_warnings": 41,
    "files_analyzed": [
      "logs\\niche_etl.log"
    ],
    "summary": {
      "has_errors": true,
      "has_warnings": true,
      "error_categories": {
        "Database": 17,
        "Other": 16
      },
      "warning_categories": {
        "Database": 20,
        "Other": 21
      },
      "most_common_errors": [],
      "most_common_warnings": []
    }
  },
  "email_sent": true,
  "log_rotation": {
    "files_deleted": [],
    "files_kept": [
      "logs\\niche_etl.log"
    ],
    "total_deleted": 0,
    "total_kept": 1,
    "space_freed": 0
  }
}
2025-06-18 14:26:19 | INFO | __main__:main:215 | Niche Text ETL completed successfully
