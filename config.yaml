# Database Configuration
database:
  source:
    driver: "ODBC Driver 17 for SQL Server"
    server: "HQDCPRUARMSDBRP"
    database: "NicheRMSReport"
    db_schema: "dbo"
    username: "PARead"
    password: "PARead"
    trusted_connection: false # Set to false if using SQL authentication
    connection_timeout: 30
    command_timeout: 300

  destination:
    driver: "ODBC Driver 17 for SQL Server"
    server: "HQPRCAD94BI03"
    database: "PA_PROD"
    db_schema: "dbo"
    username: "PAAd<PERSON>"
    password: "PA@dmin"
    trusted_connection: false # Set to false if using SQL authentication
    connection_timeout: 30
    command_timeout: 300

# Processing Configuration
processing:
  batch_size: 1000
  max_workers: 4
  max_retries: 3
  retry_delay: 5  # seconds
  memory_limit_mb: 2048
  max_records_to_process: 100  # Set to a number (e.g., 100, 200) to limit processing for testing/debug
                                # Set to null or 0 for unlimited processing (production mode)

  # Content type categories to process
  categories_to_process:
    - "ms_word"
    - "ms_excel"
    - "pdf"
    - "markup"
    - "niche_markup"
    - "text"

  # Categories to ignore
  categories_to_ignore:
    - "other"
    - "image"
    - "video"
    - "audio"

# Database Objects Configuration
database_objects:
  main_processed_documents_table: "NicheBlobETLStaging"
  etl_checkpoints_table: "NicheBlobETLCheckpoints"
  processing_statistics_table: "NicheBlobETLStatistics"
  document_chunk_embeddings_table: "NicheBlobETLEmbeddings"
  processed_documents_view: "vw_NicheBlobETLStaging"
  update_statistics_procedure: "sp_UpdateNicheBlobETLStatistics"
  initial_checkpoint_process_name: "niche_text_etl"

# Text Embedding Configuration (using all-MiniLM-L6-v2 model)
embedding:
  enabled: true  # Enabled by default with local model

  # Embedding model configuration
  model:
    provider: "sentence_transformers"  # Using sentence-transformers for local processing
    model_name: "all-MiniLM-L6-v2"  # Efficient and high-quality model
    max_tokens: 512  # Maximum tokens per embedding request (appropriate for this model)

  # Processing configuration
  processing:
    chunk_size: 1000  # Characters per text chunk for large documents
    chunk_overlap: 100  # Overlap between chunks
    batch_size: 32  # Number of texts to embed in one batch (optimized for local processing)

  # Storage configuration
  storage:
    embedding_column: "text_embedding"  # Column name for main embedding
    chunk_embeddings_table: "DocumentChunkEmbeddings"  # Table for chunk embeddings
    embedding_dimension: 384  # Dimension for all-MiniLM-L6-v2 model

  # API configuration (for external providers)
  api:
    timeout: 30  # API timeout in seconds
    max_retries: 3  # Maximum retry attempts
    rate_limit_delay: 0.1  # Minimal delay for local processing

# Logging Configuration
logging:
  level: "INFO"
  log_dir: "logs"
  log_file: "niche_etl.log"
  max_file_size_mb: 10
  backup_count: 10
  retention_days: 30
  
  # Log format
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"

# Email Configuration
email:
  smtp_server: "smtp.haltonpolice.ca"
  smtp_port: 587
  use_tls: true
  sender: "<EMAIL>"
  username: "<EMAIL>"  # SMTP authentication username
  password: ""  # SMTP authentication password (configure as needed)
  recipients:
    - "<EMAIL>"
  
  # Alert settings
  alert_schedule: "08:00"  # Daily at 8 AM
  alert_timezone: "America/Toronto"
  
  # Email templates
  subject_template: "Niche ETL Daily Report - {date}"
  error_threshold: 1  # Send alert if >= 1 error in last 24h

# Pipeline Configuration
pipeline:
  incremental_mode: true
  checkpoint_frequency: 100  # Save progress every N batches
  
  # Table names
  source_table: "TBL_BlobData"
  destination_table: "NicheBlobETLStaging"
  checkpoint_table: "NicheBlobETLCheckpoints"
  
  # SQL query parameters
  host_id_prefixes:
    - "91423001"
    - "91623001"


# Monitoring
monitoring:
  enabled: true  # Enable automatic monitoring
  health_check_interval: 300  # seconds
  metrics_retention_days: 7
  performance_log_frequency: 1000  # Log performance every N records
  run_daily_monitoring: true  # Run daily monitoring tasks after pipeline execution

# Simplified Execution Configuration
execution:
  auto_setup_database: true  # Automatically run database setup if needed
  auto_validate_config: true  # Automatically validate configuration before running
  auto_cleanup_logs: false  # Automatically clean up old log files
  exit_on_validation_errors: true  # Exit if configuration validation fails
  exit_on_pipeline_errors: true  # Exit if pipeline execution fails
