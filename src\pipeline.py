"""Main pipeline orchestration for Niche Text ETL."""

from typing import Dict, Any, List
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime
from loguru import logger
import threading

from .config import Config
from .database import DbClient
from .decompression import Decompressor, DecompressionError
from .parsers.factory import ParserFactory
from .parsers.base import ParsingError
from .embedding.processor import EmbeddingProcessor
from .config_validator import ConfigurationValidator


class PipelineStats:
    """Thread-safe statistics tracking for pipeline execution."""
    
    def __init__(self):
        self._lock = threading.Lock()
        self.reset()
    
    def reset(self):
        """Reset all statistics."""
        with self._lock:
            self.records_processed = 0
            self.records_successful = 0
            self.records_failed = 0
            self.bytes_processed = 0
            self.text_extracted = 0
            self.start_time = None
            self.end_time = None
            self.errors = []
    
    def increment_processed(self):
        """Increment processed record count."""
        with self._lock:
            self.records_processed += 1
    
    def increment_successful(self, text_length: int = 0):
        """Increment successful record count."""
        with self._lock:
            self.records_successful += 1
            self.text_extracted += text_length
    
    def increment_failed(self, error: str = None):
        """Increment failed record count."""
        with self._lock:
            self.records_failed += 1
            if error:
                self.errors.append(error)
    
    def add_bytes_processed(self, bytes_count: int):
        """Add to bytes processed count."""
        with self._lock:
            self.bytes_processed += bytes_count
    
    def start_timing(self):
        """Start timing the pipeline."""
        with self._lock:
            self.start_time = datetime.now()
    
    def end_timing(self):
        """End timing the pipeline."""
        with self._lock:
            self.end_time = datetime.now()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get current statistics."""
        with self._lock:
            duration = None
            if self.start_time and self.end_time:
                duration = (self.end_time - self.start_time).total_seconds()
            
            return {
                'records_processed': self.records_processed,
                'records_successful': self.records_successful,
                'records_failed': self.records_failed,
                'success_rate': (self.records_successful / max(self.records_processed, 1)) * 100,
                'bytes_processed': self.bytes_processed,
                'text_extracted': self.text_extracted,
                'duration_seconds': duration,
                'records_per_second': self.records_processed / max(duration, 1) if duration else 0,
                'error_count': len(self.errors),
                'recent_errors': self.errors[-10:]  # Last 10 errors
            }


class NicheTextPipeline:
    """Main pipeline for Niche text extraction and normalization."""
    
    def __init__(self, config: Config):
        """Initialize pipeline with configuration.

        Args:
            config: Pipeline configuration
        """
        self.config = config
        self.stats = PipelineStats()

        # Validate configuration and set module flags
        self.validator = ConfigurationValidator(config)
        self.validation_result = self.validator.validate_all()

        # Store module flags for runtime decisions
        self.email_enabled = self.validation_result.module_flags.get('email_enabled', False)
        self.embedding_enabled = self.validation_result.module_flags.get('embedding_enabled', False)

        # Initialize components
        self.source_db = DbClient(config.database['source'])
        self.dest_db = DbClient(config.database['destination'])
        self.decompressor = Decompressor(config.processing.memory_limit_mb)
        self.parser_factory = ParserFactory()

        # Initialize embedding processor only if enabled
        if self.embedding_enabled:
            self.embedding_processor = EmbeddingProcessor(config.embedding)
            logger.info("Text embedding processor initialized")
        else:
            self.embedding_processor = None
            logger.info("Text embedding processor disabled")

        logger.info("Pipeline initialized successfully")
    
    def run(self) -> Dict[str, Any]:
        """Run the complete ETL pipeline.
        
        Returns:
            Dictionary with execution statistics
        """
        logger.info("Starting Niche Text ETL Pipeline")
        self.stats.reset()
        self.stats.start_timing()
        
        try:
            # Test database connections
            self._test_connections()
            
            # Get starting checkpoint
            last_processed_id = self.source_db.get_last_processed_id(
                self.config.database_objects.etl_checkpoints_table,
                self.config.database_objects.initial_checkpoint_process_name
            )
            
            # Process batches
            batch_count = 0
            total_records_processed = 0
            max_records = self.config.processing.max_records_to_process

            for batch in self.source_db.fetch_batch(
                self.config.processing.batch_size,
                last_processed_id,
                self.config.pipeline.host_id_prefixes
            ):
                # Check if we should limit processing for debug/testing
                if max_records and total_records_processed >= max_records:
                    logger.info(f"Reached debug limit of {max_records} records. Stopping processing.")
                    break

                # Trim batch if it would exceed the limit
                if max_records and total_records_processed + len(batch) > max_records:
                    remaining_records = max_records - total_records_processed
                    batch = batch[:remaining_records]
                    logger.info(f"Trimming batch to {len(batch)} records to stay within debug limit")

                batch_count += 1
                total_records_processed += len(batch)

                if max_records:
                    logger.info(f"Processing batch {batch_count} with {len(batch)} records "
                              f"(total: {total_records_processed}/{max_records})")
                else:
                    logger.info(f"Processing batch {batch_count} with {len(batch)} records "
                              f"(total: {total_records_processed})")
                
                # Process batch with threading
                processed_records = self._process_batch(batch)
                
                # Insert successful records
                if processed_records:
                    success = self.dest_db.insert_batch(
                        self.config.database_objects.main_processed_documents_table,
                        processed_records
                    )
                    
                    if success:
                        # Update checkpoint
                        max_id = max(record['SourceId'] for record in processed_records)
                        self.source_db.update_checkpoint(
                            self.config.database_objects.etl_checkpoints_table,
                            self.config.database_objects.initial_checkpoint_process_name,
                            max_id
                        )
                        logger.info(f"Checkpoint updated to ID: {max_id}")
                
                # Log progress
                if batch_count % self.config.monitoring.performance_log_frequency == 0:
                    self._log_progress()

                # Check if we've reached the debug limit
                if max_records and total_records_processed >= max_records:
                    logger.info(f"Reached debug limit of {max_records} records. Processing complete.")
                    break
            
            self.stats.end_timing()
            final_stats = self.stats.get_stats()
            logger.info(f"Pipeline completed successfully: {final_stats}")
            
            return final_stats
            
        except Exception as e:
            self.stats.end_timing()
            logger.error(f"Pipeline failed: {e}")
            raise
    
    def _test_connections(self):
        """Test database connections."""
        logger.info("Testing database connections...")
        
        if not self.source_db.test_connection():
            raise Exception("Source database connection failed")
        
        if not self.dest_db.test_connection():
            raise Exception("Destination database connection failed")
        
        logger.info("Database connections successful")
    
    def _process_batch(self, batch: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Process a batch of records with threading.
        
        Args:
            batch: List of raw records from database
            
        Returns:
            List of successfully processed records
        """
        processed_records = []
        
        with ThreadPoolExecutor(max_workers=self.config.processing.max_workers) as executor:
            # Submit all records for processing
            future_to_record = {
                executor.submit(self._process_record, record): record
                for record in batch
            }
            
            # Collect results
            for future in as_completed(future_to_record):
                record = future_to_record[future]
                try:
                    result = future.result()
                    if result:
                        processed_records.append(result)
                except Exception as e:
                    logger.error(f"Record processing failed for ID {record.get('Id', 'unknown')}: {e}")
                    self.stats.increment_failed(str(e))
        
        return processed_records

    def _process_record(self, record: Dict[str, Any]) -> Dict[str, Any]:
        """Process a single record.

        Args:
            record: Raw record from database

        Returns:
            Processed record or None if processing failed
        """
        try:
            self.stats.increment_processed()

            # Extract data and metadata
            data = record['Data']
            is_gzipped = bool(record.get('gzip', 0))
            category = record.get('category')

            if not data:
                logger.warning(f"No data for record ID {record['Id']}")
                self.stats.increment_failed("No data")
                return None

            if not category or category not in self.config.processing.categories_to_process:
                logger.debug(f"Skipping record ID {record['Id']} with category: {category}")
                self.stats.increment_failed(f"Category not processed: {category}")
                return None

            self.stats.add_bytes_processed(len(data))

            # Step 1: Decompress if needed
            try:
                decompressed_data, decomp_metadata = self.decompressor.decompress(data, is_gzipped)
            except DecompressionError as e:
                logger.error(f"Decompression failed for record ID {record['Id']}: {e}")
                self.stats.increment_failed(f"Decompression error: {e}")
                return None

            # Step 2: Parse content
            try:
                parsing_result = self.parser_factory.parse_content(decompressed_data, record)
            except ParsingError as e:
                logger.error(f"Parsing failed for record ID {record['Id']}: {e}")
                self.stats.increment_failed(f"Parsing error: {e}")
                return None

            if not parsing_result.get('parsing_successful', False):
                logger.warning(f"Parsing unsuccessful for record ID {record['Id']}")
                self.stats.increment_failed("Parsing unsuccessful")
                return None

            # Step 3: Generate embeddings (if enabled)
            normalized_text = parsing_result['normalized_text']
            if self.embedding_enabled and self.embedding_processor:
                embedding_result = self.embedding_processor.process_text(normalized_text, record['Id'])
            else:
                # Create empty embedding result when embeddings are disabled
                embedding_result = {
                    'embedding_generated': False,
                    'text_embedding': None,
                    'embedding_dimension': None,
                    'chunk_count': None,
                    'embedding_type': None
                }

            self.stats.increment_successful(len(normalized_text))

            # Build destination record with all metadata
            dest_record = {
                'SourceId': record['Id'],
                'Niche_Report_ID': record.get('Niche_Report_ID'),
                'Entered_Time': record.get('Entered_Time'),
                'Report_Time': record.get('Report_Time'),
                'Remarks': record.get('Remarks'),
                'Niche_Author_ID': record.get('Niche_Author_ID'),
                'Niche_Enter_ID': record.get('Niche_Enter_ID'),
                'Niche_Occurrence_ID': record.get('Niche_Occurrence_ID'),
                'Occurrence_Number': record.get('Occurrence_Number'),
                'Occurrence_Type': record.get('Occurrence_Type'),
                'Zone': record.get('Zone'),
                'Team': record.get('Team'),
                'Municipality': record.get('Municipality'),
                'real_type': record.get('real_type'),
                'category': category,
                'normalized_text': normalized_text,
                'text_length': len(normalized_text),
                'original_size': len(data),
                'decompressed_size': decomp_metadata.get('decompressed_size', len(data)),
                'compression_ratio': decomp_metadata.get('compression_ratio', 1.0),
                'parser_used': parsing_result.get('parser_type'),
                'ProcessedTime': datetime.now(),
                'parsing_successful': True,

                # Embedding fields (will be null if embeddings disabled)
                'text_embedding': embedding_result.get('text_embedding') if embedding_result.get('embedding_generated') else None,
                'embedding_dimension': embedding_result.get('embedding_dimension') if embedding_result.get('embedding_generated') else None,
                'chunk_count': embedding_result.get('chunk_count') if embedding_result.get('embedding_generated') else None,
                'embedding_type': embedding_result.get('embedding_type') if embedding_result.get('embedding_generated') else None
            }

            return dest_record

        except Exception as e:
            logger.error(f"Unexpected error processing record ID {record.get('Id', 'unknown')}: {e}")
            self.stats.increment_failed(f"Unexpected error: {e}")
            return None

    def _log_progress(self):
        """Log current progress statistics."""
        stats = self.stats.get_stats()
        logger.info(
            f"Progress: {stats['records_processed']} processed, "
            f"{stats['records_successful']} successful, "
            f"{stats['records_failed']} failed, "
            f"{stats['success_rate']:.1f}% success rate, "
            f"{stats['records_per_second']:.1f} records/sec"
        )

    def get_parser_info(self) -> Dict[str, Any]:
        """Get information about available parsers and embedding processor.

        Returns:
            Dictionary with parser and embedding information
        """
        info = self.parser_factory.get_parser_info()
        info['embedding'] = self.embedding_processor.get_provider_info()
        return info

    def validate_configuration(self) -> Dict[str, Any]:
        """Validate pipeline configuration.

        Returns:
            Dictionary with validation results
        """
        # Use the comprehensive validator
        validation_result = self.validation_result

        validation = {
            'valid': validation_result.valid,
            'warnings': validation_result.warnings.copy(),
            'errors': validation_result.errors.copy(),
            'module_flags': validation_result.module_flags.copy()
        }

        # Check database connections
        try:
            self._test_connections()
        except Exception as e:
            validation['valid'] = False
            validation['errors'].append(f"Database connection error: {e}")

        # Check parser availability
        parser_info = self.get_parser_info()
        if not parser_info['supported_categories']:
            validation['valid'] = False
            validation['errors'].append("No parsers available")

        # Check configuration consistency
        unsupported_categories = []
        for category in self.config.processing.categories_to_process:
            if not self.parser_factory.is_category_supported(category):
                unsupported_categories.append(category)

        if unsupported_categories:
            validation['warnings'].append(
                f"Unsupported categories in config: {unsupported_categories}"
            )

        # Check embedding configuration only if embedding processor exists
        if self.embedding_processor:
            embedding_validation = self.embedding_processor.validate_configuration()
            if embedding_validation.get('enabled', False):
                if not embedding_validation.get('valid', False):
                    validation['warnings'].extend(embedding_validation.get('errors', []))
                validation['warnings'].extend(embedding_validation.get('warnings', []))

        return validation
