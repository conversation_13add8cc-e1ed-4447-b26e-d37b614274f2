"""Text embedding processor for the ETL pipeline."""

from typing import Dict, Any, List, Optional, Tuple
import numpy as np
from loguru import logger

from ..config import EmbeddingConfig
from .factory import EmbeddingFactory
from .base import BaseEmbeddingProvider, EmbeddingError


class EmbeddingProcessor:
    """Processes text embeddings for the ETL pipeline."""
    
    def __init__(self, config: EmbeddingConfig):
        """Initialize embedding processor.
        
        Args:
            config: Embedding configuration
        """
        self.config = config
        self.factory = EmbeddingFactory()
        self.provider: Optional[BaseEmbeddingProvider] = None
        
        if config.enabled:
            self._initialize_provider()
    
    def _initialize_provider(self):
        """Initialize the embedding provider."""
        try:
            provider_config = {
                'model_name': self.config.model.model_name,
                'max_tokens': self.config.model.max_tokens,
                'timeout': self.config.api.timeout,
                'max_retries': self.config.api.max_retries,
                'rate_limit_delay': self.config.api.rate_limit_delay,
                'embedding_dimension': self.config.storage.embedding_dimension
            }
            
            self.provider = self.factory.create_provider(
                self.config.model.provider,
                provider_config
            )
            
            logger.info(f"Initialized embedding provider: {self.config.model.provider}")
            
        except Exception as e:
            logger.error(f"Failed to initialize embedding provider: {e}")
            # Fall back to mock provider for development
            self.provider = self.factory.create_provider('mock', provider_config)
            logger.warning("Falling back to mock embedding provider")
    
    def is_enabled(self) -> bool:
        """Check if embedding processing is enabled.
        
        Returns:
            True if embeddings are enabled
        """
        return self.config.enabled and self.provider is not None
    
    def process_text(self, text: str, record_id: int = None) -> Dict[str, Any]:
        """Process text and generate embeddings.
        
        Args:
            text: Input text to process
            record_id: Optional record ID for logging
            
        Returns:
            Dictionary with embedding results
        """
        if not self.is_enabled():
            return {
                'embedding_generated': False,
                'reason': 'Embedding processing disabled'
            }
        
        if not text or not text.strip():
            return {
                'embedding_generated': False,
                'reason': 'Empty text'
            }
        
        try:
            # Determine if text needs chunking
            if len(text) <= self.config.processing.chunk_size:
                # Single embedding for short text
                return self._process_single_text(text, record_id)
            else:
                # Multiple embeddings for long text
                return self._process_chunked_text(text, record_id)
                
        except Exception as e:
            logger.error(f"Embedding processing failed for record {record_id}: {e}")
            return {
                'embedding_generated': False,
                'reason': f'Processing error: {e}',
                'error': str(e)
            }
    
    def _process_single_text(self, text: str, record_id: int = None) -> Dict[str, Any]:
        """Process single text without chunking.
        
        Args:
            text: Input text
            record_id: Optional record ID
            
        Returns:
            Embedding results
        """
        try:
            embedding = self.provider.embed_text(text)
            
            result = {
                'embedding_generated': True,
                'embedding_type': 'single',
                'text_embedding': embedding.tolist(),  # Convert to list for JSON serialization
                'embedding_dimension': len(embedding),
                'text_length': len(text),
                'chunk_count': 1
            }
            
            if record_id:
                logger.debug(f"Generated single embedding for record {record_id}")
            
            return result
            
        except Exception as e:
            raise EmbeddingError(f"Single text embedding failed: {e}")
    
    def _process_chunked_text(self, text: str, record_id: int = None) -> Dict[str, Any]:
        """Process long text with chunking.
        
        Args:
            text: Input text
            record_id: Optional record ID
            
        Returns:
            Embedding results with chunks
        """
        try:
            # Split text into chunks
            chunks = self.provider.chunk_text(
                text,
                self.config.processing.chunk_size,
                self.config.processing.chunk_overlap
            )
            
            # Generate embeddings for chunks
            chunk_embeddings = []
            for i, chunk in enumerate(chunks):
                try:
                    embedding = self.provider.embed_text(chunk)
                    chunk_embeddings.append({
                        'chunk_index': i,
                        'chunk_text': chunk,
                        'chunk_embedding': embedding.tolist(),
                        'chunk_length': len(chunk)
                    })
                except Exception as e:
                    logger.warning(f"Failed to embed chunk {i} for record {record_id}: {e}")
                    continue
            
            if not chunk_embeddings:
                raise EmbeddingError("No chunks could be embedded")
            
            # Create main embedding (average of chunk embeddings)
            all_embeddings = np.array([chunk['chunk_embedding'] for chunk in chunk_embeddings])
            main_embedding = np.mean(all_embeddings, axis=0)
            
            result = {
                'embedding_generated': True,
                'embedding_type': 'chunked',
                'text_embedding': main_embedding.tolist(),
                'embedding_dimension': len(main_embedding),
                'text_length': len(text),
                'chunk_count': len(chunk_embeddings),
                'chunk_embeddings': chunk_embeddings
            }
            
            if record_id:
                logger.debug(f"Generated chunked embeddings for record {record_id}: {len(chunk_embeddings)} chunks")
            
            return result
            
        except Exception as e:
            raise EmbeddingError(f"Chunked text embedding failed: {e}")
    
    def validate_configuration(self) -> Dict[str, Any]:
        """Validate embedding configuration.
        
        Returns:
            Validation results
        """
        validation = {
            'valid': True,
            'enabled': self.config.enabled,
            'warnings': [],
            'errors': []
        }
        
        if not self.config.enabled:
            validation['warnings'].append("Embedding processing is disabled")
            return validation
        
        # Validate provider
        if self.provider:
            provider_validation = self.provider.validate_configuration()
            validation['provider_validation'] = provider_validation
            
            if not provider_validation.get('valid', False):
                validation['valid'] = False
                validation['errors'].extend(provider_validation.get('errors', []))
            
            validation['warnings'].extend(provider_validation.get('warnings', []))
        else:
            validation['valid'] = False
            validation['errors'].append("No embedding provider initialized")
        
        # Validate configuration values
        if self.config.processing.chunk_size <= 0:
            validation['errors'].append("Chunk size must be positive")
        
        if self.config.processing.chunk_overlap >= self.config.processing.chunk_size:
            validation['errors'].append("Chunk overlap must be less than chunk size")
        
        if self.config.storage.embedding_dimension <= 0:
            validation['errors'].append("Embedding dimension must be positive")
        
        validation['valid'] = len(validation['errors']) == 0
        return validation
    
    def get_provider_info(self) -> Dict[str, Any]:
        """Get information about the current embedding provider.
        
        Returns:
            Provider information
        """
        if not self.provider:
            return {
                'provider': None,
                'enabled': False
            }
        
        return {
            'provider': self.provider.__class__.__name__,
            'model_name': self.config.model.model_name,
            'embedding_dimension': self.provider.get_embedding_dimension(),
            'enabled': self.config.enabled,
            'chunk_size': self.config.processing.chunk_size,
            'chunk_overlap': self.config.processing.chunk_overlap
        }
