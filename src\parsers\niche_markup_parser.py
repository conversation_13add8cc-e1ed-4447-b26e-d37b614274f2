"""Niche markup parser for custom Niche formats."""

from typing import Dict, Any
import re
from bs4 import BeautifulSoup
from loguru import logger
from .base import BaseParser, ParsingError


class NicheMarkupParser(BaseParser):
    """Parser for Niche-specific markup formats (nrt, nxdx)."""
    
    @property
    def supported_types(self) -> list:
        """Return list of supported content types."""
        return ['niche_markup', 'nrt', 'nxdx']
    
    def parse(self, data: bytes, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Parse Niche markup content and extract text.
        
        Args:
            data: Raw Niche markup data
            metadata: Content metadata
            
        Returns:
            Dictionary with parsed results
            
        Raises:
            ParsingError: If parsing fails
        """
        try:
            # Decode the content
            content = self.safe_decode(data)
            
            # Determine specific Niche format
            niche_format = self._detect_niche_format(content, metadata)
            
            # Parse based on detected format
            if niche_format == 'nrt':
                text = self._parse_nrt(content)
            elif niche_format == 'nxdx':
                text = self._parse_nxdx(content)
            else:
                # Generic Niche markup parsing
                text = self._parse_generic_niche(content)
            
            # Normalize the extracted text
            normalized_text = self.normalize_text(text)
            
            parsing_metadata = {
                'niche_format': niche_format,
                'encoding_used': self.detect_encoding(data),
                'original_length': len(data),
                'content_length': len(content),
                'extracted_length': len(text),
                'normalized_length': len(normalized_text)
            }
            
            return self.create_result(normalized_text, metadata, parsing_metadata)
            
        except Exception as e:
            raise ParsingError(f"Failed to parse Niche markup content: {e}")
    
    def _detect_niche_format(self, content: str, metadata: Dict[str, Any]) -> str:
        """Detect specific Niche format.
        
        Args:
            content: Decoded content string
            metadata: Content metadata
            
        Returns:
            Detected Niche format
        """
        # Check metadata first
        real_type = metadata.get('real_type', '').lower()
        if real_type in ['nrt', 'nxdx']:
            return real_type
        
        # Check content patterns
        content_lower = content.lower()
        
        if 'nrt' in content_lower or '<nrt' in content_lower:
            return 'nrt'
        elif 'nxdx' in content_lower or '<nxdx' in content_lower:
            return 'nxdx'
        else:
            return 'generic'
    
    def _parse_nrt(self, content: str) -> str:
        """Parse NRT (Niche Report Template) format.

        Args:
            content: NRT content string

        Returns:
            Extracted text
        """
        try:
            # NRT files use a custom bracket-based markup format
            # Parse the specific NRT structure we observed

            text = self._parse_nrt_structure(content)

            # Clean up any remaining NRT patterns
            text = self._clean_nrt_patterns(text)

            return text

        except Exception as e:
            logger.warning(f"NRT structure parsing failed, falling back to regex: {e}")
            # Fallback to regex-based cleaning
            return self._parse_with_regex(content, 'nrt')

    def _parse_nrt_structure(self, content: str) -> str:
        """Parse NRT-specific bracket structure.

        Args:
            content: NRT content string

        Returns:
            Extracted and formatted text
        """
        # Remove the outer container
        text = content
        if text.startswith('{ntf1{') and text.endswith('}}'):
            text = text[6:-2]  # Remove {ntf1{ and }}

        # Process different NRT elements in order
        text = self._process_nrt_links(text)  # Remove links first to avoid interference
        text = self._process_nrt_sections(text)  # Process sections
        text = self._process_nrt_tables(text)  # Process tables
        text = self._process_nrt_formatting(text)  # Remove formatting tags
        text = self._process_nrt_paragraphs(text)  # Convert paragraph markers

        # Final cleanup
        text = self._cleanup_nrt_artifacts(text)

        return text

    def _process_nrt_sections(self, text: str) -> str:
        """Process NRT section headers like {p1l{b ...}} and {p2l{b ...}}.

        Args:
            text: Input text

        Returns:
            Text with section headers processed
        """
        # Pattern for section headers: {p1l{b SECTION_NAME}} or {p2l{b SECTION_NAME}}
        # Handle p1l and p2l separately for better control

        # Level 1 headers (p1l)
        p1_pattern = r'\{p1l\{b\s*([^}]+)\s*\}\}'
        def replace_p1(match):
            section_name = match.group(1).strip()
            return f"\n\n=== {section_name.upper()} ===\n"
        text = re.sub(p1_pattern, replace_p1, text)

        # Level 2 headers (p2l)
        p2_pattern = r'\{p2l\{b\s*([^}]+)\s*\}\}'
        def replace_p2(match):
            section_name = match.group(1).strip()
            return f"\n\n=== {section_name.upper()} ===\n"
        text = re.sub(p2_pattern, replace_p2, text)

        # Clean up any remaining p1l/p2l tags
        text = re.sub(r'\{p[12]l[^}]*\}', '', text)

        return text

    def _process_nrt_tables(self, text: str) -> str:
        """Process NRT table structures like {t{tc ...}{tr{c{...}}{c{...}}}}.

        Args:
            text: Input text

        Returns:
            Text with tables processed
        """
        # First remove table container tags
        text = re.sub(r'\{t\{tc[^}]*\}\{tc\}', '', text)
        text = re.sub(r'\{t\{tc[^}]*\}', '', text)

        # Use a simpler approach: find table rows iteratively
        # Pattern for simple table rows without nested braces first
        simple_row_pattern = r'\{tr\{c\{([^{}]*)\}\}\{c\{([^{}]*)\}\}\}'

        def replace_simple_row(match):
            col1 = match.group(1).strip()
            col2 = match.group(2).strip()

            # Clean up any remaining markup in columns
            col1 = self._clean_column_content(col1)
            col2 = self._clean_column_content(col2)

            if col1 and col2:
                return f"{col1}: {col2}\n"
            elif col1:
                return f"{col1}\n"
            elif col2:
                return f"{col2}\n"
            else:
                return ""

        # Process simple table rows first
        text = re.sub(simple_row_pattern, replace_simple_row, text)

        # Clean up any remaining table row artifacts
        text = re.sub(r'\{tr[^}]*\}', '', text)

        return text

    def _clean_column_content(self, content: str) -> str:
        """Clean content within table columns.

        Args:
            content: Column content

        Returns:
            Cleaned content
        """
        # Remove common formatting tags within columns
        content = re.sub(r'\{pal\}', '', content)  # Remove paragraph markers
        content = re.sub(r'pal\s*', '', content)  # Remove standalone 'pal' text
        content = re.sub(r'\{pil\s*([^}]*)\}', r'\1', content)  # Extract pil content
        content = re.sub(r'pil\s*', '', content)  # Remove standalone 'pil' text
        content = re.sub(r'\{b\s*([^}]*)\}', r'\1', content)  # Extract bold content

        # Clean up any remaining simple tags
        content = re.sub(r'\{[^}]*\}', '', content)

        # Normalize whitespace
        content = re.sub(r'\s+', ' ', content)

        return content.strip()

    def _process_nrt_links(self, text: str) -> str:
        """Process NRT link structures like {lnk{nav ...}DISPLAY_TEXT}.

        Args:
            text: Input text

        Returns:
            Text with links processed
        """
        # Pattern for links: {lnk{nav ...}DISPLAY_TEXT}
        link_pattern = r'\{lnk\{nav[^}]*\}([^}]*)\}'

        def replace_link(match):
            display_text = match.group(1).strip()
            # Only keep meaningful display text, skip navigation placeholders
            if display_text and not display_text.startswith('nicherms:') and display_text != 'CLICK HERE and either scroll and select OR start typing':
                return display_text
            return ""

        return re.sub(link_pattern, replace_link, text)

    def _process_nrt_formatting(self, text: str) -> str:
        """Process NRT formatting tags like {b ...}, {rt ...}.

        Args:
            text: Input text

        Returns:
            Text with formatting processed
        """
        # Bold text: {b TEXT}
        text = re.sub(r'\{b\s*([^}]*)\}', r'\1', text)

        # Rich text: {rt TEXT}
        text = re.sub(r'\{rt\s*([^}]*)\}', r'\1', text)

        return text

    def _process_nrt_paragraphs(self, text: str) -> str:
        """Process NRT paragraph markers like {pal}.

        Args:
            text: Input text

        Returns:
            Text with paragraph breaks processed
        """
        # Replace paragraph markers with line breaks
        text = re.sub(r'\{pal\}', '\n', text)
        text = re.sub(r'\{pil\s*([^}]*)\}', r'\1', text)

        return text

    def _cleanup_nrt_artifacts(self, text: str) -> str:
        """Clean up remaining NRT artifacts and normalize text.

        Args:
            text: Input text

        Returns:
            Cleaned text
        """
        # Remove any remaining curly braces and their content (iteratively)
        while '{' in text and '}' in text:
            old_text = text
            text = re.sub(r'\{[^{}]*\}', '', text)
            if text == old_text:  # No more changes, break to avoid infinite loop
                break

        # Remove any remaining isolated braces
        text = re.sub(r'[{}]', '', text)

        # Normalize whitespace
        text = re.sub(r'\n\s*\n\s*\n+', '\n\n', text)  # Multiple newlines to double
        text = re.sub(r'[ \t]+', ' ', text)  # Multiple spaces to single
        text = re.sub(r'^\s+|\s+$', '', text, flags=re.MULTILINE)  # Trim lines

        # Remove empty lines at start and end
        text = text.strip()

        return text

    def _parse_nxdx(self, content: str) -> str:
        """Parse NXDX format.

        Args:
            content: NXDX content string

        Returns:
            Extracted text
        """
        try:
            # NXDX files are XML-based with Node/Cell structure
            # Parse the specific NXDX structure we observed

            text = self._parse_nxdx_structure(content)

            # Clean up any remaining NXDX patterns
            text = self._clean_nxdx_patterns(text)

            return text

        except Exception as e:
            logger.warning(f"NXDX structure parsing failed, falling back to regex: {e}")
            # Fallback to regex-based cleaning
            return self._parse_with_regex(content, 'nxdx')

    def _parse_nxdx_structure(self, content: str) -> str:
        """Parse NXDX-specific XML structure.

        Args:
            content: NXDX content string

        Returns:
            Extracted and formatted text
        """
        try:
            soup = BeautifulSoup(content, 'xml')

            # Find the root Data node
            data_node = soup.find('Data')
            if not data_node:
                # Fallback to parsing the whole content
                data_node = soup

            text_parts = []

            # Process the main occurrence node
            occurrence_node = data_node.find('Node', {'Name': 'Occurrence'})
            if occurrence_node:
                text_parts.extend(self._process_nxdx_occurrence(occurrence_node))

            # Process other top-level nodes
            for node in data_node.find_all('Node', recursive=False):
                if node.get('Name') != 'Occurrence':
                    node_text = self._process_nxdx_node(node)
                    if node_text:
                        text_parts.append(node_text)

            return '\n\n'.join(text_parts)

        except Exception as e:
            logger.error(f"NXDX XML parsing failed: {e}")
            # Fallback to simple text extraction
            soup = BeautifulSoup(content, 'xml')
            return soup.get_text()

    def _process_nxdx_occurrence(self, occurrence_node) -> list:
        """Process the main Occurrence node.

        Args:
            occurrence_node: BeautifulSoup node for Occurrence

        Returns:
            List of text sections
        """
        sections = []

        # Extract basic occurrence information
        basic_info = []
        for cell in occurrence_node.find_all('Cell', recursive=False):
            name = cell.get('Name', '')
            value = cell.get('Value', '')

            if value and name:
                # Clean up the field names for display
                display_name = self._clean_field_name(name)
                basic_info.append(f"{display_name}: {value}")

        if basic_info:
            sections.append("=== OCCURRENCE DETAILS ===\n" + '\n'.join(basic_info))

        # Process child nodes
        for child_node in occurrence_node.find_all('Node', recursive=False):
            node_text = self._process_nxdx_node(child_node)
            if node_text:
                sections.append(node_text)

        return sections

    def _process_nxdx_node(self, node) -> str:
        """Process a generic NXDX node.

        Args:
            node: BeautifulSoup node

        Returns:
            Formatted text for the node
        """
        node_name = node.get('Name', '')
        if not node_name:
            return ""

        # Create section header
        section_title = self._clean_field_name(node_name)
        text_parts = [f"\n=== {section_title.upper()} ==="]

        # Extract direct cell values
        cells = []
        for cell in node.find_all('Cell', recursive=False):
            name = cell.get('Name', '')
            value = cell.get('Value', '')

            if value and name:
                display_name = self._clean_field_name(name)
                cells.append(f"{display_name}: {value}")

        if cells:
            text_parts.extend(cells)

        # Process child nodes recursively
        for child_node in node.find_all('Node', recursive=False):
            child_text = self._process_nxdx_child_node(child_node, indent="  ")
            if child_text:
                text_parts.append(child_text)

        return '\n'.join(text_parts) if len(text_parts) > 1 else ""

    def _process_nxdx_child_node(self, node, indent="") -> str:
        """Process a child NXDX node with indentation.

        Args:
            node: BeautifulSoup node
            indent: Indentation string

        Returns:
            Formatted text for the child node
        """
        node_name = node.get('Name', '')
        leaf_spec = node.get('LeafSpecialization', '')

        # Create a meaningful title
        if leaf_spec:
            title = f"{indent}{self._clean_field_name(leaf_spec)}"
        elif node_name:
            title = f"{indent}{self._clean_field_name(node_name)}"
        else:
            title = f"{indent}Details"

        text_parts = [title + ":"]

        # Extract cell values
        for cell in node.find_all('Cell', recursive=False):
            name = cell.get('Name', '')
            value = cell.get('Value', '')

            if value and name:
                display_name = self._clean_field_name(name)
                text_parts.append(f"{indent}  {display_name}: {value}")

        # Process nested nodes
        for child_node in node.find_all('Node', recursive=False):
            child_text = self._process_nxdx_child_node(child_node, indent + "  ")
            if child_text:
                text_parts.append(child_text)

        return '\n'.join(text_parts) if len(text_parts) > 1 else ""

    def _clean_field_name(self, field_name: str) -> str:
        """Clean up NXDX field names for display.

        Args:
            field_name: Raw field name

        Returns:
            Cleaned field name
        """
        # Handle specific field mappings first (with original suffixes)
        field_mappings = {
            'OccurrenceFileNoG': 'File Number',
            'OccurrenceStdOccTypeRId': 'Occurrence Type',
            'ReportedTimeTZV2G': 'Reported Time',
            'StartTimeTZV2G': 'Start Time',
            'EndTimeTZV2G': 'End Time',
            'CivicSiteStreetNumberG': 'Street Number',
            'StreetNameG': 'Street Name',
            'MunicipalityNameG': 'Municipality',
            'ProvStateCodeG': 'Province',
            'PostalZipCodeG': 'Postal Code',
            'DateOfBirthG': 'Date of Birth',
            'GOccIvPA': 'Physical Address',
            'GOccIvCommAddress': 'Communication Address',
            'GOccIvGPerson': 'Person Information',
            'GOccIvGNVProperty': 'Property Information',
            'GPersonName': 'Name',
            'GPersonAssocGAddress': 'Address',
            'GPersonSubjectEmployment': 'Employment',
            'PhysicalAddress': 'Physical Address',
            'ValueCurrentG': 'Current Value',
            'ValueDamageG': 'Damage Value',
            'SurnameG': 'Surname',
            'Given1G': 'Given Name',
            'GenderG': 'Gender',
            'EmployerG': 'Employer',
            'ClassificationG': 'Classification',
            'UCRLocationTypeG': 'Location Type',
            'Summary': 'Summary'
        }

        # Check for exact match first
        if field_name in field_mappings:
            return field_mappings[field_name]

        # Remove common suffixes
        field_name = re.sub(r'G$', '', field_name)  # Remove trailing 'G'
        field_name = re.sub(r'RId$', '', field_name)  # Remove 'RId' suffix
        field_name = re.sub(r'_L$', '', field_name)  # Remove '_L' suffix

        # Check mappings again after suffix removal
        if field_name in field_mappings:
            return field_mappings[field_name]

        # Convert camelCase to readable format
        field_name = re.sub(r'([a-z])([A-Z])', r'\1 \2', field_name)

        return field_name.title()

    def _parse_generic_niche(self, content: str) -> str:
        """Parse generic Niche markup.
        
        Args:
            content: Generic Niche content string
            
        Returns:
            Extracted text
        """
        # Try multiple approaches
        
        # 1. Try as XML/HTML
        try:
            soup = BeautifulSoup(content, 'html.parser')
            text = soup.get_text()
            if text.strip():
                return self._clean_generic_niche_patterns(text)
        except Exception:
            pass
        
        # 2. Try regex-based cleaning
        try:
            return self._parse_with_regex(content, 'generic')
        except Exception:
            pass
        
        # 3. Last resort: return content with minimal cleaning
        return self._minimal_clean(content)
    
    def _clean_nrt_patterns(self, text: str) -> str:
        """Clean NRT-specific patterns from text.

        Args:
            text: Raw extracted text

        Returns:
            Cleaned text
        """
        # Remove common NRT metadata and navigation patterns
        patterns = [
            r'NRT_VERSION:\s*[\d.]+',
            r'TEMPLATE_ID:\s*\w+',
            r'CREATED_DATE:\s*[\d/\-\s:]+',
            r'MODIFIED_DATE:\s*[\d/\-\s:]+',
            r'nicherms:[^}]+',  # Remove nicherms navigation commands
            r'Type UCR here \(eg[^)]+\)',  # Remove UCR placeholder text
            r'CLICK HERE and either scroll and select OR start typing',  # Remove UI instructions
            r'\+ Add [^}]+Information',  # Remove "Add Information" buttons
            r'\+ Select Template',  # Remove template selection buttons
            r'</?nrt[^>]*>',
            r'</?template[^>]*>',
            r'</?field[^>]*>',
        ]

        for pattern in patterns:
            text = re.sub(pattern, '', text, flags=re.IGNORECASE)

        return text
    
    def _clean_nxdx_patterns(self, text: str) -> str:
        """Clean NXDX-specific patterns from text.

        Args:
            text: Raw extracted text

        Returns:
            Cleaned text
        """
        # Remove common NXDX metadata and XML artifacts
        patterns = [
            r'NXDX_VERSION:\s*[\d.]+',
            r'DOCUMENT_ID:\s*\w+',
            r'</?nxdx[^>]*>',
            r'</?document[^>]*>',
            r'</?section[^>]*>',
            r'</?Data[^>]*>',
            r'</?Node[^>]*>',
            r'</?Cell[^>]*>',
            r'ReferenceId="\d+"',
            r'NormalReference="\d+"',
            r'LeafSpecialization="[^"]*"',
            r'\bundefined\b',  # Remove 'undefined' words
            r'^\s*undefined\s*$',  # Remove lines with only 'undefined'
        ]

        for pattern in patterns:
            text = re.sub(pattern, '', text, flags=re.IGNORECASE)

        # Clean up empty lines and normalize whitespace
        text = re.sub(r'\n\s*\n\s*\n+', '\n\n', text)  # Multiple newlines to double
        text = re.sub(r'^\s+|\s+$', '', text, flags=re.MULTILINE)  # Trim lines
        text = re.sub(r'\n{3,}', '\n\n', text)  # Limit to max 2 consecutive newlines

        # Remove any remaining "undefined" entries
        text = re.sub(r'\bundefined\b\s*', '', text)
        text = re.sub(r'^\s*undefined\s*$', '', text, flags=re.MULTILINE)

        # Final cleanup
        text = text.strip()

        return text
    
    def _clean_generic_niche_patterns(self, text: str) -> str:
        """Clean generic Niche patterns from text.
        
        Args:
            text: Raw extracted text
            
        Returns:
            Cleaned text
        """
        # Remove common Niche system patterns
        patterns = [
            r'NICHE_ID:\s*\w+',
            r'SYSTEM_GENERATED:\s*[\w\s]+',
            r'</?niche[^>]*>',
            r'</?system[^>]*>',
        ]
        
        for pattern in patterns:
            text = re.sub(pattern, '', text, flags=re.IGNORECASE)
        
        return text
    
    def _parse_with_regex(self, content: str, format_type: str) -> str:
        """Parse content using regex patterns when XML parsing fails.
        
        Args:
            content: Content string
            format_type: Format type for specific patterns
            
        Returns:
            Extracted text
        """
        # Remove XML/HTML tags
        text = re.sub(r'<[^>]+>', '', content)
        
        # Remove common markup patterns
        text = re.sub(r'&\w+;', ' ', text)  # HTML entities
        text = re.sub(r'\{\{[^}]+\}\}', '', text)  # Template variables
        text = re.sub(r'\[\[[^\]]+\]\]', '', text)  # Wiki-style links
        
        # Apply format-specific cleaning
        if format_type == 'nrt':
            text = self._clean_nrt_patterns(text)
        elif format_type == 'nxdx':
            text = self._clean_nxdx_patterns(text)
        else:
            text = self._clean_generic_niche_patterns(text)
        
        return text
    
    def _minimal_clean(self, content: str) -> str:
        """Minimal cleaning for content that can't be parsed otherwise.
        
        Args:
            content: Content string
            
        Returns:
            Minimally cleaned text
        """
        # Just remove obvious markup and normalize whitespace
        text = re.sub(r'<[^>]+>', ' ', content)
        text = re.sub(r'&\w+;', ' ', text)
        text = re.sub(r'\s+', ' ', text)
        return text.strip()
