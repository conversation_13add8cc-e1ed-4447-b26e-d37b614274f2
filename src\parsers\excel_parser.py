"""Excel parser for extracting text from MS Excel documents."""

from typing import Dict, Any
import io
from .base import BaseParser, ParsingError


class ExcelParser(BaseParser):
    """Parser for Microsoft Excel documents."""
    
    @property
    def supported_types(self) -> list:
        """Return list of supported content types."""
        return ['ms_excel', 'xls', 'xlsx']
    
    def parse(self, data: bytes, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Parse Excel document and extract text.
        
        Args:
            data: Raw Excel document data
            metadata: Content metadata
            
        Returns:
            Dictionary with parsed results
            
        Raises:
            ParsingError: If parsing fails
        """
        try:
            # Try openpyxl first (for .xlsx files)
            try:
                text = self._parse_with_openpyxl(data)
                parser_used = 'openpyxl'
            except Exception as e1:
                # Fallback to textract (handles both .xls and .xlsx)
                try:
                    text = self._parse_with_textract(data)
                    parser_used = 'textract'
                except Exception as e2:
                    raise ParsingError(f"Both Excel parsers failed. openpyxl: {e1}, textract: {e2}")
            
            # Normalize the extracted text
            normalized_text = self.normalize_text(text)
            
            parsing_metadata = {
                'parser_used': parser_used,
                'original_length': len(data),
                'extracted_length': len(text),
                'normalized_length': len(normalized_text)
            }
            
            return self.create_result(normalized_text, metadata, parsing_metadata)
            
        except Exception as e:
            raise ParsingError(f"Failed to parse Excel document: {e}")
    
    def _parse_with_openpyxl(self, data: bytes) -> str:
        """Parse Excel document using openpyxl.
        
        Args:
            data: Raw Excel document data
            
        Returns:
            Extracted text
            
        Raises:
            Exception: If parsing fails
        """
        try:
            from openpyxl import load_workbook
            
            # Create BytesIO object from data
            excel_file = io.BytesIO(data)
            
            # Load workbook
            workbook = load_workbook(excel_file, data_only=True)
            
            text_parts = []
            
            # Process each worksheet
            for sheet_name in workbook.sheetnames:
                worksheet = workbook[sheet_name]
                
                # Add sheet name as header
                text_parts.append(f"[SHEET: {sheet_name}]")
                
                # Extract data from cells
                sheet_data = []
                for row in worksheet.iter_rows(values_only=True):
                    # Filter out None values and convert to strings
                    row_data = [str(cell) for cell in row if cell is not None]
                    if row_data:  # Only add non-empty rows
                        sheet_data.append(' | '.join(row_data))
                
                if sheet_data:
                    text_parts.extend(sheet_data)
                    text_parts.append("")  # Add blank line between sheets
            
            text = '\n'.join(text_parts)
            
            return text
            
        except ImportError:
            raise Exception("openpyxl not available")
        except Exception as e:
            raise Exception(f"openpyxl parsing failed: {e}")
    
    def _parse_with_textract(self, data: bytes) -> str:
        """Parse Excel document using textract.
        
        Args:
            data: Raw Excel document data
            
        Returns:
            Extracted text
            
        Raises:
            Exception: If parsing fails
        """
        try:
            import textract
            import tempfile
            import os
            
            # textract requires a file path, so create a temporary file
            with tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx') as temp_file:
                temp_file.write(data)
                temp_file_path = temp_file.name
            
            try:
                # Extract text using textract
                text_bytes = textract.process(temp_file_path)
                text = text_bytes.decode('utf-8')
                
                return text
                
            finally:
                # Clean up temporary file
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)
            
        except ImportError:
            raise Exception("textract not available")
        except Exception as e:
            raise Exception(f"textract parsing failed: {e}")
    
    def _validate_excel_data(self, data: bytes) -> bool:
        """Validate that data appears to be a valid Excel document.
        
        Args:
            data: Raw data to validate
            
        Returns:
            True if data appears to be an Excel document
        """
        if len(data) < 4:
            return False
        
        # Check for common Excel document signatures
        # .xlsx files start with PK (ZIP signature)
        if data.startswith(b'PK'):
            return True
        
        # .xls files have OLE2 signature
        if data.startswith(b'\xd0\xcf\x11\xe0'):
            return True
        
        return False
