"""Decompression module for handling gzip and raw data."""

import gzip
import io
from typing import Union, <PERSON><PERSON>
from loguru import logger


class DecompressionError(Exception):
    """Custom exception for decompression errors."""
    pass


class Decompressor:
    """Handles decompression of gzip and raw data."""
    
    def __init__(self, max_size_mb: int = 100):
        """Initialize decompressor.
        
        Args:
            max_size_mb: Maximum allowed decompressed size in MB
        """
        self.max_size_bytes = max_size_mb * 1024 * 1024
    
    def decompress(self, data: bytes, is_gzipped: bool) -> Tuple[bytes, dict]:
        """Decompress data if needed and return metadata.
        
        Args:
            data: Raw data bytes
            is_gzipped: Whether the data is gzip compressed
            
        Returns:
            Tuple of (decompressed_data, metadata_dict)
            
        Raises:
            DecompressionError: If decompression fails or size exceeds limit
        """
        metadata = {
            'original_size': len(data),
            'is_compressed': is_gzipped,
            'compression_ratio': 1.0,
            'decompression_successful': False
        }
        
        try:
            if is_gzipped:
                decompressed_data = self._decompress_gzip(data)
                metadata['decompressed_size'] = len(decompressed_data)
                metadata['compression_ratio'] = len(data) / len(decompressed_data) if decompressed_data else 1.0
            else:
                decompressed_data = data
                metadata['decompressed_size'] = len(data)
            
            # Check size limit
            if len(decompressed_data) > self.max_size_bytes:
                raise DecompressionError(
                    f"Decompressed data size ({len(decompressed_data)} bytes) "
                    f"exceeds limit ({self.max_size_bytes} bytes)"
                )
            
            metadata['decompression_successful'] = True
            logger.debug(f"Decompression successful: {metadata}")
            
            return decompressed_data, metadata
            
        except Exception as e:
            logger.error(f"Decompression failed: {e}")
            metadata['error'] = str(e)
            raise DecompressionError(f"Failed to decompress data: {e}")
    
    def _decompress_gzip(self, data: bytes) -> bytes:
        """Decompress gzip data.
        
        Args:
            data: Gzip compressed data
            
        Returns:
            Decompressed bytes
            
        Raises:
            DecompressionError: If gzip decompression fails
        """
        try:
            # Create BytesIO object from the compressed data
            compressed_stream = io.BytesIO(data)
            
            # Use gzip.GzipFile to decompress
            with gzip.GzipFile(fileobj=compressed_stream, mode='rb') as gz_file:
                decompressed_data = gz_file.read()
            
            logger.debug(f"Gzip decompression: {len(data)} -> {len(decompressed_data)} bytes")
            return decompressed_data
            
        except gzip.BadGzipFile as e:
            raise DecompressionError(f"Invalid gzip data: {e}")
        except Exception as e:
            raise DecompressionError(f"Gzip decompression error: {e}")
    
    def is_likely_compressed(self, data: bytes) -> bool:
        """Check if data appears to be gzip compressed based on magic bytes.
        
        Args:
            data: Data to check
            
        Returns:
            True if data appears to be gzip compressed
        """
        if len(data) < 2:
            return False
        
        # Check for gzip magic number (0x1f, 0x8b)
        return data[0] == 0x1f and data[1] == 0x8b
    
    def validate_decompressed_content(self, data: bytes, expected_type: str = None) -> dict:
        """Validate decompressed content and provide insights.
        
        Args:
            data: Decompressed data
            expected_type: Expected content type (optional)
            
        Returns:
            Dictionary with validation results
        """
        validation = {
            'is_valid': True,
            'content_type_hints': [],
            'encoding_detected': None,
            'size_bytes': len(data),
            'warnings': []
        }
        
        if not data:
            validation['is_valid'] = False
            validation['warnings'].append("Empty data after decompression")
            return validation
        
        # Try to detect encoding
        try:
            # Try UTF-8 first
            data.decode('utf-8')
            validation['encoding_detected'] = 'utf-8'
        except UnicodeDecodeError:
            try:
                # Try UTF-16
                data.decode('utf-16')
                validation['encoding_detected'] = 'utf-16'
            except UnicodeDecodeError:
                validation['encoding_detected'] = 'binary'
                validation['warnings'].append("Could not detect text encoding")
        
        # Content type hints based on data patterns
        if data.startswith(b'%PDF'):
            validation['content_type_hints'].append('pdf')
        elif data.startswith(b'PK'):
            validation['content_type_hints'].append('office_document')  # ZIP-based formats
        elif data.startswith(b'<'):
            validation['content_type_hints'].append('xml_or_html')
        elif b'<!DOCTYPE html' in data[:1000].lower():
            validation['content_type_hints'].append('html')
        elif b'<?xml' in data[:100]:
            validation['content_type_hints'].append('xml')
        
        # Size warnings
        if len(data) > 50 * 1024 * 1024:  # 50MB
            validation['warnings'].append("Large file size may impact processing performance")
        
        return validation
